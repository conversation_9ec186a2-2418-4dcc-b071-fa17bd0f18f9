Please write Python code to extract JsonSchema xpaths which can be used in Crawl4AI to scrape data from a webpage.
For given Pydantic class, code should generate xpaths for each field in the class. 
**Instructions:**
- Convert Pydantic class to JsonSchema
- Extract xpaths for each field in the JsonSchema from the html
- Append the xpaths to the JsonSchema
- Return the JsonSchema with xpaths
**Implementation:**
- Use Crawl4AI to scrape the webpage for the given url
- Use crawl4ai strategy to extract xpaths

# Reference:
Here's the crawl4ai documentation, please refer to it for more details: 
 - [Configuration](https://docs.crawl4ai.com/core/browser-crawler-config/)
 - [Crawling](https://docs.crawl4ai.com/core/simple-crawling/)
 - [Result](https://docs.crawl4ai.com/core/crawler-result/)
 - [LLM Integration](https://docs.crawl4ai.com/extraction/llm-strategies/)

**Strict guidelines:**
- use only the above documentation to implement the code
- do not use any other libraries or tools
- do not hardcode the xpaths, use the crawl4ai to extract the xpaths
- do not use any other documentation or code snippets on crawl4ai usage


# Rental Property Search Agent

## Objective
Create a CrewAI agents that automates the process of searching for **rental properties** on real estate websites. The agent should gather relevant property details and store them in a JSON file.

Here;s an example of Agents https://docs.crewai.com/concepts/tools

**Implementation Details**:
   - Use **Python**, **CrewAI**, **CrewAI[tools]**, **CrewAI[agents]**, **CrewAI[tasks]**

## Requirements for different agents
1. **Input Handling**  
   - **Direct Link Provided:** If a specific link to a real estate listing page is given, the agent will use only that link for the search.  
   - **No Direct Link:** If no link is provided, the agent should:
     1. Search Google for a suitable real estate website.
     2. Navigate to that website.
     3. Conduct a rental property search on the site.

2. **Browser Automation**  
   - Open a web browser to access the target real estate website.  
   - Fill out the necessary search form fields to find rental properties (e.g., location, property type, price range) and submit the search.  
   - Click through the resulting listings to view property details.

3. **Data Extraction**  
   - For each property listing, extract:
     - **url**: The direct link to the listing.  
     - **title**: The property’s headline (e.g., "2-bedroom apartment with city view").  
     - **price**: Monthly rental cost (e.g., "$1,800/month").  
     - **location**: The address or general neighborhood.  
     - **property_type**: The type of property (e.g., "Apartment", "Condo").  
     - **property_size**: The area of the property (e.g., "1,200 sq ft", "110 m2").  
     - **property_features**: A list of key features (bedrooms, bathrooms, pool, parking, etc.).  
   - If a specific piece of information is unavailable, leave it blank.

4. **JSON Output**  
   - Save the extracted information to a JSON file with the following format:
     ```json
     {
       "url": "https://www.example.com/property/123",
       "title": "Beautiful 3-bedroom house in the heart of the city",
       "price": "$1,500/month",
       "location": "New York, NY",
       "property_type": "House",
       "property_size": "2,500 sq ft",
       "property_features": ["3 bedrooms", "2 bathrooms", "1 garage", "Pool"]
     }
     ```

5. **Implementation Details**  
   - Provide well-organized and clearly documented code for the CrewAI agent.  
   - The agent should handle basic error scenarios (e.g., timeouts, missing elements) gracefully.  
   - The workflow should be straightforward to follow and update.