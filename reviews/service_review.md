# Unbiased Service Review Analysis

**Objective:** To conduct a comprehensive and detailed analysis of user reviews for a specific service, filtering out marketing and promotional content to ensure an unbiased assessment. The final output should be a summarized report detailing positive and negative sentiment trends, supported by data on the number of reviews and their sources, with a specific focus on a predefined list of factors.

**Service for Analysis:** `{{ service_name }}`

**Factors to Focus On:**  
`{{ factors_to_focus_on }}`


### **Instructions for the LLM:**

1.  **Search for User Reviews:**
    * Initiate a thorough search for user reviews of **`{{ service_name }}`** across the internet.
    * Prioritize reviews from independent and reputable platforms known for unbiased user feedback. Focus your search on sources such as:
        * Trustpilot
        * Yelp
        * Google Reviews
        * G2 Crowd (for B2B services)
        * SiteJabber
        * Relevant online forums and communities (e.g., Reddit threads in subreddits like r/services or service-specific communities)

2.  **Filter for Unbiased Content:**
    * Critically analyze the language and tone of the collected reviews to identify and **exclude** any content that appears to be marketing material or brand-promoted.
    * Disregard reviews that exhibit characteristics of paid or incentivized feedback, such as overly enthusiastic and generic praise, use of marketing jargon, or a primary focus on brand messaging rather than genuine user experience.
    * Isolate and focus on reviews that provide specific, verifiable details about the user's personal experience with the service, including both its strengths and weaknesses.

3.  **Analyze Sentiment Trends based on Factors:**
    * Categorize the filtered, unbiased reviews into positive, negative, and neutral sentiments.
    * For each of the **Factors to Focus On** provided above, identify and quantify the most frequently mentioned **positive aspects** of the service.
    * Similarly, for each factor, identify and quantify the most frequently cited **negative points** or complaints.

4.  **Generate a Data-Driven Report:**
    * Compile the findings into a clear and concise summary report using the structure defined below.
    * The report must begin with a high-level overview of the general sentiment.
    * Present the key positive and negative sentiment trends as distinct sections, organized by the specified factors where applicable.
    * For each trend, provide specific examples from the reviews, quoting short, relevant snippets where appropriate to illustrate the point.
    * Include a quantitative analysis, stating the total number of unbiased reviews analyzed and a breakdown of the number of reviews sourced from each platform.
    * Conclude with an overall assessment of the service based *only* on the gathered user feedback.

---

### **Required Output Structure:**

# Service Review Analysis: `{{ service_name }}`

**Overall Sentiment:** `[e.g., Generally Positive, Mixed with Key Reservations, Overwhelmingly Negative]`

**Executive Summary:**  
`[A brief, one-paragraph summary of the key findings, including the most significant pros and cons for {{ service_name }}, referencing the key factors.]`

---

## **Positive Sentiment Trends**

### **1. [Key Positive Theme, e.g., Responsive Customer Support]**
* **Factor:** `[e.g., Responsiveness & Support]`
* **Description:** `[Brief description of the trend and why users appreciate it.]`
* **Supporting Data:** `[e.g., Mentioned in approximately 70% of positive reviews.]`
* **Example Snippet:**  
  `[e.g., "The support team resolved my issue within minutes — truly outstanding service."]`

### **2. [Key Positive Theme, e.g., Seamless Onboarding Process]**
* **Factor:** `[e.g., Ease of Use & Onboarding]`
* **Description:** `[Brief description of the trend and why users appreciate it.]`
* **Supporting Data:** `[e.g., Praised for its straightforward setup across multiple platforms.]`
* **Example Snippet:**  
  `[e.g., "Getting started was a breeze, the tutorial walked me through everything step by step."]`

*(...add more positive trends as identified, referencing the factors above...)*

---

## **Negative Sentiment Trends**

### **1. [Key Negative Theme, e.g., Occasional Downtime]**
* **Factor:** `[e.g., Reliability & Uptime]`
* **Description:** `[Brief description of the trend and the problems it causes for users.]`
* **Supporting Data:** `[e.g., Reported by 35% of negative reviews, especially on Trustpilot.]`
* **Example Snippet:**  
  `[e.g., "The service went offline several times last week, which disrupted our workflow."]`

### **2. [Key Negative Theme, e.g., Hidden Fees]**
* **Factor:** `[e.g., Pricing Transparency]`
* **Description:** `[Brief description of the trend and user concerns.]`
* **Supporting Data:** `[e.g., Mentioned by numerous users on Yelp and G2.]`
* **Example Snippet:**  
  `[e.g., "They charged me more than the quoted price with unexpected add-ons."]`

*(...add more negative trends as identified, referencing the factors above...)*

---

## **Review Source Analysis**

* **Total Unbiased Reviews Analyzed:** `[e.g., 920]`
* **Source Breakdown:**
    * Trustpilot: `[e.g., 300 reviews]`
    * Yelp: `[e.g., 250 reviews]`
    * Google Reviews: `[e.g., 180 reviews]`
    * G2 Crowd: `[e.g., 120 reviews]`
    * SiteJabber: `[e.g., 70 reviews]`

---

## **Conclusion**

`[Provide a final, balanced assessment of the perceived quality and value of {{ service_name }} based exclusively on the comprehensive review analysis, summarizing the findings across the key input factors.]`