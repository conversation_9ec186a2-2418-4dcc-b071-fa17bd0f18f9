# Unbiased Product Review Analysis

**Objective:** To conduct a comprehensive and detailed analysis of user reviews for a specific product, filtering out marketing and promotional content to ensure an unbiased assessment. The final output should be a summarized report detailing positive and negative sentiment trends, supported by data on the number of reviews and their sources, with a specific focus on a predefined list of factors.

**Product for Analysis:** `{{ product_name }}`

**Factors to Focus On:**
`{{ factors_to_focus_on }}`


### **Instructions for the LLM:**

1.  **Search for User Reviews:**
    * Initiate a thorough search for user reviews of **`{{ product_name }}`** across the internet.
    * Prioritize reviews from independent and reputable platforms known for unbiased user feedback. Focus your search on sources such as:
        * Trustpilot
        * Amazon (customer reviews section)
        * Google Reviews
        * Reputable technology or consumer watchdog websites (e.g., CNET, Consumer Reports - where applicable)
        * Relevant online forums and communities (e.g., Reddit threads in subreddits like r/BuyItForLife or product-specific communities)

2.  **Filter for Unbiased Content:**
    * Critically analyze the language and tone of the collected reviews to identify and **exclude** any content that appears to be marketing material or brand-promoted.
    * Disregard reviews that exhibit characteristics of paid or incentivized feedback, such as overly enthusiastic and generic praise, use of marketing jargon, or a primary focus on brand messaging rather than genuine user experience.
    * Isolate and focus on reviews that provide specific, verifiable details about the user's personal experience with the product, including both its strengths and weaknesses.

3.  **Analyze Sentiment Trends based on Factors:**
    * Categorize the filtered, unbiased reviews into positive, negative, and neutral sentiments.
    * For each of the **Factors to Focus On** provided above, identify and quantify the most frequently mentioned **positive aspects** of the product.
    * Similarly, for each factor, identify and quantify the most frequently cited **negative points** or complaints.

4.  **Generate a Data-Driven Report:**
    * Compile the findings into a clear and concise summary report using the structure defined below.
    * The report must begin with a high-level overview of the general sentiment.
    * Present the key positive and negative sentiment trends as distinct sections, organized by the specified factors where applicable.
    * For each trend, provide specific examples from the reviews, quoting short, relevant snippets where appropriate to illustrate the point.
    * Include a quantitative analysis, stating the total number of unbiased reviews analyzed and a breakdown of the number of reviews sourced from each platform.
    * Conclude with an overall assessment of the product based *only* on the gathered user feedback.

---

### **Required Output Structure:**

# Product Review Analysis: `{{ product_name }}`

**Overall Sentiment:** `[e.g., Generally Positive, Mixed with Key Reservations, Overwhelmingly Negative]`

**Executive Summary:** `[A brief, one-paragraph summary of the key findings, including the most significant pros and cons for {{ product_name }}, referencing the key factors.]`

---

## **Positive Sentiment Trends**

### **1. [Key Positive Theme, e.g., Exceptional Battery Life]**
* **Factor:** `[e.g., Battery Life]`
* **Description:** `[Brief description of the trend and why users appreciate it.]`
* **Supporting Data:** `[e.g., Mentioned in approximately 65% of positive reviews.]`
* **Example Snippet:** `[e.g., "The battery on this thing is incredible, I can easily go two full days without needing to charge."]`

### **2. [Key Positive Theme, e.g., Intuitive User Interface]**
* **Factor:** `[e.g., User Experience & Ease of Use]`
* **Description:** `[Brief description of the trend and why users appreciate it.]`
* **Supporting Data:** `[e.g., Praised for its ease of use across multiple platforms.]`
* **Example Snippet:** `[e.g., "The software is so easy to navigate; I figured everything out in the first 10 minutes without touching the manual."]`

*(...add more positive trends as identified, referencing the factors above...)*

---

## **Negative Sentiment Trends**

### **1. [Key Negative Theme, e.g., Unreliable Connectivity]**
* **Factor:** `[e.g., Software & Connectivity]`
* **Description:** `[Brief description of the trend and the problems it causes for users.]`
* **Supporting Data:** `[e.g., A common complaint, found in 40% of negative reviews, particularly on Amazon.]`
* **Example Snippet:** `[e.g., "It constantly disconnects from my Wi-Fi, making it unreliable for any task that requires a stable connection."]`

### **2. [Key Negative Theme, e.g., Flimsy Build Quality]**
* **Factor:** `[e.g., Build Quality & Durability]`
* **Description:** `[Brief description of the trend and user concerns.]`
* **Supporting Data:** `[e.g., Mentioned by numerous users on both Reddit and Trustpilot.]`
* **Example Snippet:** `[e.g., "The plastic casing feels cheap and I'm worried it will crack if I drop it even from a small height."]`

*(...add more negative trends as identified, referencing the factors above...)*

---

## **Review Source Analysis**

* **Total Unbiased Reviews Analyzed:** `[e.g., 850]`
* **Source Breakdown:**
    * Amazon: `[e.g., 450 reviews]`
    * Trustpilot: `[e.g., 200 reviews]`
    * Google Reviews: `[e.g., 120 reviews]`
    * Reddit: `[e.g., 80 reviews]`

---

## **Conclusion**

`[Provide a final, balanced assessment of the perceived quality and value of {{ product_name }} based exclusively on the comprehensive review analysis, summarizing the findings across the key input factors.]`