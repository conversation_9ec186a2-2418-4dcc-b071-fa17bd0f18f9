# LLM for summarisation 
please suggest the most suitable LLM for summarisation. 
perhaps find the leaderboard or comparison for summarisation models. I am looking for a model that can summarise a text into a short summary. 
It will be more important to have a model that can summarise a text into a short summary and can focus on certain topic/subject.
Ideally, there should be a model to summarise reviews and another model for sentiment analysis and to create rating from summary.
Reviews summarisation has to be done in a way that model can pick on each review and trends in reviews. 
Model shouldn't ignore in case multiple reviews are suggesting same thing.

please help me compare BART, Pegasus, Claude and GPT-4o for summarisation.

# Summarisation focus on certain topic/subject
Is it possible to use model like bart and have them focus os certain topic/subject or not loose any information about certain topic/subject during summarisation ?


2.	Two-Step Pipeline with Specialized Models:
	•	Step 1: Use a top-notch summarization model (e.g., BART-Large-CNN or PEGASUS) to condense reviews into a short summary.
	•	Step 2: Feed the summary into a sentiment or rating classification model (fine-tuned from BERT, RoBERTa, or DistilBERT) to obtain a final rating.
