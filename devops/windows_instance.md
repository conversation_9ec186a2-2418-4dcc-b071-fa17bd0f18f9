You are an AI systems architect. Your task is to design a cost-efficient and automated plan for deploying a Windows-based instance on AWS.

Objectives:
	1.	Identify the most suitable AWS service for hosting an ad-hoc Windows instance, considering all available options (e.g., EC2, Lightsail, WorkSpaces, etc.).
	2.	Ensure that the solution:
	•	Uses a Windows operating system (e.g., Windows Server 2019/2022).
	•	Is deployed using Infrastructure as Code (IaC) via Terraform.
	•	Supports on-demand/ad-hoc usage, minimizing runtime when idle.
	•	Includes automatic monitoring (e.g., via CloudWatch) to detect inactivity and stop the instance to save costs.
	•	Can be manually or programmatically restarted from the stopped state without recreating resources.

Deliverables:
	•	A step-by-step plan for deploying the solution, including:
	•	Choice of AWS service and justification.
	•	Terraform configuration structure (VPC, IAM, instance, EBS, etc.).
	•	Mechanism for automatic idle shutdown (e.g., using CloudWatch alarms or Lambda).
	•	Method to restart the instance from a stopped state (via CLI, console, Lambda, etc.).

Please provide:
	•	Terraform code snippets and recommended modules.
	•	Any required IAM policies or role definitions.
	•	Architecture diagram in Markdown (text) or mermaid format.