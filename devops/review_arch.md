# LLM Inference System on AWS – Infrastructure Plan

## Overview

I'm looking for the most suitable AWS service to run LLM inference for testing purposes with following requirements:

1. **Minimal cost** – The service will only be used to run test cases, so cost-efficiency is a priority (e.g., spot pricing or short-term usage).
2. **Fast startup and shutdown** – I need a solution that boots up quickly and can be stopped just as easily.
3. **Automatic idle shutdown** – The service should support automatic shutdown after a configurable period of inactivity.
4. **Persistent model storage** – I want to download models (e.g., from Hugging Face) once and keep them available across sessions to avoid redownloading.

Please review the following architecture and optimize it for cost-efficiency and scalability, if needed.
Suggest the list of changes and provide reasoning for each change.

## Architecture Components

**Auto Scaling lifecycle with Lambda hooks (attach/detach EBS)**: The instance launch is paused for a custom lifecycle action (Lambda attaches EBS) before proceeding.

## Step-by-Step Deployment Instructions

### 1. Setup AWS VPC & Subnets

Provision a new VPC (if none named "llm-vpc" exists) with subnets for EC2:

```terraform
# Terraform: VPC (created only if no existing VPC with name "llm-vpc")
data "aws_vpc" "existing_vpc" {
  filter { name = "tag:Name"; values = ["llm-vpc"] }
}

resource "aws_vpc" "llm_vpc" {
  count       = data.aws_vpc.existing_vpc.id == "" ? 1 : 0
  cidr_block  = "10.0.0.0/16"
  tags        = { Name = "llm-vpc" }
}

# Public subnet in a specific AZ (e.g., us-east-1a) for EC2
resource "aws_subnet" "llm_subnet" {
  vpc_id                  = coalesce(data.aws_vpc.existing_vpc.id, aws_vpc.llm_vpc[0].id)
  cidr_block              = "********/24"
  availability_zone       = "us-east-1a"  # ensure same AZ as EBS volume
  map_public_ip_on_launch = true
  tags = { Name = "llm-public-subnet" }
}

# Internet Gateway and Route Table for internet access
resource "aws_internet_gateway" "igw" {
  vpc_id = coalesce(data.aws_vpc.existing_vpc.id, aws_vpc.llm_vpc[0].id)
}

resource "aws_route_table" "public_rt" {
  vpc_id = coalesce(data.aws_vpc.existing_vpc.id, aws_vpc.llm_vpc[0].id)
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }
}

resource "aws_route_table_association" "public_assoc" {
  subnet_id      = aws_subnet.llm_subnet.id
  route_table_id = aws_route_table.public_rt.id
}
```

**Description**: Configures a new VPC and subnet (if not found) for the EC2 Spot instance. The subnet is in one AZ to match the EBS volume's AZ for attachment.

### 2. Container Image & ECR Setup

Build your LLM Docker image and host on ECR:

```terraform
# Terraform: ECR repository for Docker image
resource "aws_ecr_repository" "llm_repo" {
  name                 = "llm-inference-repo"
  image_tag_mutability = "MUTABLE"
}
```

```bash
# Shell: Build and push Docker image to ECR
aws ecr get-login-password --region <your-region> | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.<your-region>.amazonaws.com
docker build -t llm-inference:latest .
docker tag llm-inference:latest ${AWS_ACCOUNT_ID}.dkr.ecr.<your-region>.amazonaws.com/llm-inference-repo:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.<your-region>.amazonaws.com/llm-inference-repo:latest
```

**Description**: Creates an ECR repo and pushes the LLM inference Docker image. The EC2 instance will pull this image at runtime.

### 3. Persistent EBS Volume for Model Cache

Create an EBS volume to cache model files for reuse across instance restarts:

```terraform
# Terraform: Persistent EBS volume (not deleted on termination)
resource "aws_ebs_volume" "model_cache" {
  availability_zone = "us-east-1a"         # same AZ as the EC2 instance
  size              = 100                 # size in GB (adjust as needed for model data)
  type              = "gp3"
  encrypted         = true
  tags = { Name = "llm-model-cache" }
}
```

**Description**: This volume stores model data and will be attached to the instance on launch and detached on termination to preserve data. Ensure the AZ matches the EC2's subnet.

### 4. IAM Roles and Instance Profile

Define IAM roles for EC2 and Lambda functions:

```terraform
# Terraform: IAM role for EC2 (Instance Profile)
resource "aws_iam_role" "ec2_role" {
  name = "llm-ec2-role"
  assume_role_policy = <<-JSON
    {
      "Version": "2012-10-17",
      "Statement": [{ "Effect": "Allow", "Principal": { "Service": "ec2.amazonaws.com" }, "Action": "sts:AssumeRole" }]
    }
    JSON
}

resource "aws_iam_role_policy_attachment" "ec2_ecr" {
  role       = aws_iam_role.ec2_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"  # Allow pulling from ECR
}

resource "aws_iam_role_policy_attachment" "ec2_ssm" {
  role       = aws_iam_role.ec2_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"        # SSM for running commands
}

resource "aws_iam_instance_profile" "ec2_instance_profile" {
  name = "llm-ec2-instance-profile"
  role = aws_iam_role.ec2_role.name
}

# IAM role for Lambda functions (attach/detach & idle shutdown)
resource "aws_iam_role" "lambda_role" {
  name = "llm-lambda-role"
  assume_role_policy = <<-JSON
    {
      "Version": "2012-10-17",
      "Statement": [{ "Effect": "Allow", "Principal": { "Service": "lambda.amazonaws.com" }, "Action": "sts:AssumeRole" }]
    }
    JSON
}

resource "aws_iam_policy" "lambda_policy" {
  name   = "llm-lambda-policy"
  policy = <<-JSON
    {
      "Version": "2012-10-17",
      "Statement": [
        { "Effect": "Allow", "Action": [ "ec2:AttachVolume", "ec2:DetachVolume", "ec2:DescribeVolumes", "ec2:DescribeInstances" ], "Resource": "*" },
        { "Effect": "Allow", "Action": [ "autoscaling:CompleteLifecycleAction", "autoscaling:DescribeAutoScalingInstances", "autoscaling:UpdateAutoScalingGroup", "autoscaling:SetDesiredCapacity" ], "Resource": "*" },
        { "Effect": "Allow", "Action": [ "ssm:SendCommand" ], "Resource": "*" },  # (Optional) if using SSM to unmount
        { "Effect": "Allow", "Action": [ "cloudwatch:PutMetricData" ], "Resource": "*" },  # (Optional) if Lambda logs custom metrics
        { "Effect": "Allow", "Action": [ "logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents" ], "Resource": "*" }
      ]
    }
    JSON
}

resource "aws_iam_role_policy_attachment" "lambda_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}
```

**Description**: EC2 role permits pulling images from ECR and using SSM. Lambda role allows attaching/detaching EBS, completing lifecycle hooks, adjusting ASG, and logging. (Adjust IAM policies as needed for least privilege.)

### 5. Launch Template for Spot EC2

Create a Launch Template for the g5.xlarge Spot instance, with user-data to run Docker:

```terraform
# Terraform: Launch Template for Spot instance
resource "aws_launch_template" "llm_template" {
  name_prefix   = "llm-inference-lt"
  image_id      = data.aws_ami.gpu_ami.id          # Use a GPU-enabled AMI (Deep Learning AMI or similar)
  instance_type = "g5.xlarge"
  iam_instance_profile { name = aws_iam_instance_profile.ec2_instance_profile.name }
  instance_market_options {
    market_type = "spot"
    spot_options {
      # Set behavior on interruption, etc. (default is terminate)
      instance_interruption_behavior = "terminate"
    }
  }
  user_data = base64encode(<<-SCRIPT
           #!/bin/bash
           set -xe
           # Install Docker (if not pre-installed in the AMI)
           amazon-linux-extras install -y docker && systemctl start docker
           # Install NVIDIA Container Toolkit (for GPU access in Docker)
           curl -s -L https://nvidia.github.io/nvidia-docker/amazon-linux2/nvidia-docker.repo | tee /etc/yum.repos.d/nvidia-docker.repo
           yum install -y nvidia-docker2 && systemctl restart docker
           # Wait for EBS volume to attach (device appears as /dev/nvme1n1 on Nitro instances)
           until lsblk | grep -q nvme1n1; do sleep 5; done
           if ! file -s /dev/nvme1n1 | grep -q ext4; then mkfs.ext4 /dev/nvme1n1; fi
           mkdir -p /mnt/model-cache && mount /dev/nvme1n1 /mnt/model-cache
           # Login to ECR and pull the LLM Docker image
           eval $(aws ecr get-login-password --region ${aws_region} | docker login --username AWS --password-stdin ${aws_ecr_repository.llm_repo.repository_url})
           docker pull ${aws_ecr_repository.llm_repo.repository_url}:latest
           # Run the LLM inference container (using all GPUs, mounting the cache volume)
           docker run --gpus all -d -v /mnt/model-cache:/model-cache -p 80:80 --name llm-service ${aws_ecr_repository.llm_repo.repository_url}:latest
    SCRIPT
  )
  tag_specifications {
    resource_type = "instance"
    tags = { Name = "llm-inference-spot" }
  }
}

data "aws_ami" "gpu_ami" {
  # Find a recent Amazon Linux 2 Deep Learning Base AMI (GPU) in region
  most_recent = true
  owners      = ["amazon"]
  filter { name = "name"; values = ["Deep Learning AMI GPU Base * (Amazon Linux 2 *)"] }
}
```

**Description**: Defines a launch template for a g5.xlarge Spot instance. User-data installs Docker + NVIDIA runtime, mounts the EBS volume, and runs the container. The AMI should be a GPU-enabled OS with NVIDIA drivers (e.g., AWS Deep Learning Base AMI) to support CUDA.

### 6. Auto Scaling Group (0–1 capacity)

Create an ASG that can scale from 0 to 1 instance, using the Launch Template:

```terraform
# Terraform: Auto Scaling Group with min=0, max=1 (Spot instances)
resource "aws_autoscaling_group" "llm_asg" {
  name                = "llm-inference-asg"
  max_size            = 1
  min_size            = 0
  desired_capacity    = 0        # start with 0 instances (scaled down)
  health_check_type   = "EC2"
  launch_template {
    id      = aws_launch_template.llm_template.id
    version = "$Latest"
  }
  vpc_zone_identifier = [ aws_subnet.llm_subnet.id ]
  tags = [
    { key = "Name", value = "llm-inference-spot", propagate_at_launch = true }
  ]
  # Lifecycle hooks for instance launch and termination
  initial_lifecycle_hook {
    name                  = "AttachVolumeHook"
    lifecycle_transition  = "autoscaling:EC2_INSTANCE_LAUNCHING"
    default_result        = "ABANDON"          # if lifecycle action times out or fails
    heartbeat_timeout     = 300                # wait up to 5 minutes for attach
    notification_target_arn = aws_sns_topic.asg_lifecycle.arn
    role_arn              = aws_iam_role.lambda_role.arn
  }
  initial_lifecycle_hook {
    name                  = "DetachVolumeHook"
    lifecycle_transition  = "autoscaling:EC2_INSTANCE_TERMINATING"
    default_result        = "CONTINUE"
    heartbeat_timeout     = 300                # wait up to 5 minutes for detach
    notification_target_arn = aws_sns_topic.asg_lifecycle.arn
    role_arn              = aws_iam_role.lambda_role.arn
  }
}

# SNS topic for ASG lifecycle events (to trigger Lambdas)
resource "aws_sns_topic" "asg_lifecycle" {
  name = "llm-asg-lifecycle"
}

resource "aws_sns_topic_subscription" "asg_lc_lambda_sub" {
  topic_arn = aws_sns_topic.asg_lifecycle.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.attach_volume.arn
  filter_policy = jsonencode({ "LifecycleTransition": ["autoscaling:EC2_INSTANCE_LAUNCHING"] })
}

resource "aws_sns_topic_subscription" "asg_lc_lambda_sub2" {
  topic_arn = aws_sns_topic.asg_lifecycle.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.detach_volume.arn
  filter_policy = jsonencode({ "LifecycleTransition": ["autoscaling:EC2_INSTANCE_TERMINATING"] })
}
```

**Description**: The ASG is configured with min=0, max=1 to allow zero instances (cost-saving idle state). It uses the Launch Template for Spot instances and defines lifecycle hooks on Launch and Terminate events. These hooks send notifications to an SNS topic, which will invoke our Lambda functions to attach or detach the EBS volume. The instance will wait (paused in the lifecycle state) until the Lambda signals completion of the volume attachment, ensuring the volume is available before the instance becomes active. The termination hook similarly detaches the volume prior to fully terminating the instance.

### 7. Lambda Functions for Volume Attachment/Detachment

Deploy two Lambda functions to handle the ASG hooks:

```terraform
# Terraform: Lambda functions for attach and detach
resource "aws_lambda_function" "attach_volume" {
  function_name = "LLMAttachVolume"
  role          = aws_iam_role.lambda_role.arn
  runtime       = "python3.9"
  handler       = "lambda_function.lambda_handler"
  timeout       = 60
  source_code_hash = filebase64sha256("lambda_attach.zip")
  filename         = "lambda_attach.zip"   # Zip containing the Python code (see below)
  environment {
    variables = { EBS_VOLUME_ID = aws_ebs_volume.model_cache.id }
  }
}

resource "aws_lambda_function" "detach_volume" {
  function_name = "LLMDetachVolume"
  role          = aws_iam_role.lambda_role.arn
  runtime       = "python3.9"
  handler       = "lambda_function.lambda_handler"
  timeout       = 60
  source_code_hash = filebase64sha256("lambda_detach.zip")
  filename         = "lambda_detach.zip"
  environment {
    variables = { EBS_VOLUME_ID = aws_ebs_volume.model_cache.id }
  }
}

# Permissions for SNS to invoke Lambda
resource "aws_lambda_permission" "allow_sns_attach" {
  statement_id  = "AllowSNSInvokeAttach"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.attach_volume.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.asg_lifecycle.arn
}

resource "aws_lambda_permission" "allow_sns_detach" {
  statement_id  = "AllowSNSInvokeDetach"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.detach_volume.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.asg_lifecycle.arn
}
```

#### Lambda Code – Attach Volume (lambda_attach.py)

Uses Boto3 to attach the EBS volume and signal lifecycle completion:

```python
import os, boto3

autoscale = boto3.client('autoscaling')
ec2 = boto3.client('ec2')

def lambda_handler(event, context):
    # Parse lifecycle event details
    detail = event['Records'][0]['Sns']['Message']  # SNS message (string in JSON)
    message = eval(detail)  # convert string to dict (or use json.loads)
    asg_name = message['AutoScalingGroupName']
    hook_name = message['LifecycleHookName']
    token = message['LifecycleActionToken']
    instance_id = message['EC2InstanceId']
    volume_id = os.environ['EBS_VOLUME_ID']
    
    # Attach the EBS volume to the new instance
    ec2.attach_volume(InstanceId=instance_id, VolumeId=volume_id, Device='/dev/sdf')
    
    # (Optional) Wait until volume is attached (or use instance status checks)
    
    # Signal ASG to continue the launch
    autoscale.complete_lifecycle_action(AutoScalingGroupName=asg_name,
                                       LifecycleHookName=hook_name,
                                       LifecycleActionToken=token,
                                       LifecycleActionResult='CONTINUE')
```

#### Lambda Code – Detach Volume (lambda_detach.py)

Detaches the EBS volume on instance termination:

```python
import os, boto3, time

autoscale = boto3.client('autoscaling')
ec2 = boto3.client('ec2')
ssm = boto3.client('ssm')

def lambda_handler(event, context):
    detail = event['Records'][0]['Sns']['Message']
    message = eval(detail)
    asg_name = message['AutoScalingGroupName']
    hook_name = message['LifecycleHookName']
    token = message['LifecycleActionToken']
    instance_id = message['EC2InstanceId']
    volume_id = os.environ['EBS_VOLUME_ID']
    
    # (Optional) Send SSM command to instance to unmount volume before detaching
    try:
        ssm.send_command(InstanceIds=[instance_id], DocumentName="AWS-RunShellScript",
                         Parameters={ "commands": ["umount /mnt/model-cache || true"] })
        time.sleep(10)  # wait for unmount
    except Exception as e:
        print(f"SSM unmount failed: {e}")
    
    # Detach the EBS volume
    ec2.detach_volume(InstanceId=instance_id, VolumeId=volume_id, Force=True)
    
    # Wait until volume is detached
    waiter = ec2.get_waiter('volume_available')
    waiter.wait(VolumeIds=[volume_id])
    
    # Signal ASG to continue termination
    autoscale.complete_lifecycle_action(AutoScalingGroupName=asg_name,
                                       LifecycleHookName=hook_name,
                                       LifecycleActionToken=token,
                                       LifecycleActionResult='CONTINUE')
```

**Description**: These Lambda functions are invoked via SNS when the ASG launches or terminates an instance. The AttachVolume function attaches the designated EBS volume to the new EC2 (ensuring persistent model data is available), then calls CompleteLifecycleAction to resume instance startup. The DetachVolume function (on termination) optionally uses SSM to unmount the volume, detaches it, waits for detachment, and signals the ASG to complete termination. This guarantees the volume is free to attach to the next instance. The lifecycle hooks ensure the volume is detached from the old instance before attaching to the new, preserving cached data across restarts.

### 8. Idle Timeout Auto-Shutdown

Use CloudWatch and Lambda to terminate the instance after inactivity:

```terraform
# Terraform: CloudWatch Alarm for low CPU (idle) & Idle Shutdown Lambda
resource "aws_cloudwatch_metric_alarm" "idle_alarm" {
  alarm_name          = "LLMIdleAlarm"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  statistic           = "Average"
  period              = 300                     # 5 minutes interval
  evaluation_periods  = 2
  threshold           = 5                       # CPU % threshold for idleness
  comparison_operator = "LessThanThreshold"
  dimensions = { AutoScalingGroupName = aws_autoscaling_group.llm_asg.name }
  alarm_actions       = [aws_sns_topic.idle_topic.arn]
}

resource "aws_sns_topic" "idle_topic" {
  name = "llm-idle-shutdown"
}

resource "aws_sns_topic_subscription" "idle_lambda_sub" {
  topic_arn = aws_sns_topic.idle_topic.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.idle_shutdown.arn
}

resource "aws_lambda_function" "idle_shutdown" {
  function_name = "LLMIdleShutdown"
  role          = aws_iam_role.lambda_role.arn
  runtime       = "python3.9"
  handler       = "lambda_function.lambda_handler"
  timeout       = 15
  source_code_hash = filebase64sha256("lambda_idle.zip")
  filename         = "lambda_idle.zip"
}

resource "aws_lambda_permission" "allow_sns_idle" {
  statement_id  = "AllowSNSInvokeIdle"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.idle_shutdown.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.idle_topic.arn
}
```

#### Lambda Code – Idle Shutdown (lambda_idle.py)

Triggered by the CloudWatch alarm to scale down the ASG to 0:

```python
import boto3

autoscale = boto3.client('autoscaling')

def lambda_handler(event, context):
    # Identify the Auto Scaling Group to scale down
    ASG_NAME = event['Records'][0]['Sns']['MessageAttributes']['AutoScalingGroupName']['Value']
    
    # Set desired capacity to 0 (terminate instance)
    autoscale.update_auto_scaling_group(AutoScalingGroupName=ASG_NAME, DesiredCapacity=0)
```

**Description**: A CloudWatch alarm watches the instance's CPU usage. If the average CPU remains below 5% for 10 minutes (idle period), the alarm triggers. The IdleShutdown Lambda (subscribed via SNS) will then set the ASG desired capacity to 0, initiating a scale-in. This will cause the running instance to terminate when idle, freeing resources. (The ASG's termination hook will ensure the volume detaches on this scale-in event, as configured above.)

### 9. Terraform Deployment

Apply the Terraform configuration:

```bash
terraform init  
terraform apply -var aws_region=<your-region> -auto-approve
```

## System Operation

Once deployed, the system will start with 0 instances.

**Scaling Up**: Increase desired capacity to 1 (via AWS console or CLI) or configure an event to set it to 1 when needed – the ASG will launch a Spot instance, attach the EBS volume, and run the container.

**Scaling Down**: After periods of inactivity, the CloudWatch alarm triggers the Lambda to scale down to 0, shutting down the instance to save costs. The next launch will reattach the same cached model volume, avoiding re-downloads and speeding up initialization.