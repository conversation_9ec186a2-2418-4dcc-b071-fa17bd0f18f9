# Websites Deployment
You are a DevOps engineer AI tasked with architecting a setup to deploy multiple websites to a cloud server.
Here the projects which need to be deployed:
1 WebsiteA
WebsiteA contains backend in Python and frontend in SvelteKit.
2 WebsiteB
WebsiteB contains backend in Python and frontend in SvelteKit.
3 WebsiteC
WebsiteC contains frontend in SvelteKit.

Deployement should be done using Caddy server and docker. Each website will have it's own url and should be redirected by Caddy to it's respective endpoint.
Please suggest the ideal setup to acheive this. Setup should be easier to manage and scale.



# please add bash script to build docker image and push to docker hub.
# please add bash script to build backend and frontend docker images and push to docker hub.