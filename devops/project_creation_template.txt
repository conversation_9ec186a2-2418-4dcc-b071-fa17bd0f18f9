# Project setup from template config.yaml file
# Please help me create a template for project creation from a config.yaml file
# The template should be a python script that will be used to create the project based on the config.yaml file
# The script should be able to create the project in the current directory if project_name is same as the current directory 
# name otherwise it should create the project in the current directory with the name of the project
# The script should be able to create the project in the current directory if project_name is same as the current directory 
# name otherwise it should create the project in the current directory with the name of the project
# Here is teh sample config.yaml

project:
    name: project_name
    description: project_description
    type: lib or workspace or service
    
github:
    repo_name: repo_name
    add_on_github: true or false # Check if the GitHub repo already exists before creating. use github cli for this.
docker:
    docker_image: true or false
    docker_compose: true or false
    use_existing_docker_image: image_name, in this case we will not create a docker image but will use the existing image

devcontainer:
    add_devcontainer: true or false
service_ports:
    ports:
        - port_number
        - port_number

if porject is lib it will be a library and we will not create a docker image or docker compose file
if porject is workspace it will be a workspace and we will create a docker image or docker compose file
if porject is service it will be a service and we will create a docker image and docker compose file
# add Error handling for each step, with informative messages.
# add logging for each step, with informative messages using loguru.
# please also add the ability to create a project interactively by passing flag to script to create a project interactively. 
# in interactive mode defaults should be read from the config.yaml file and user can change the defaults if they want to.
# please also create sample config.yaml for each of the project type.
# please also add the Readme.md file to the project with the example of how to use the script.
# Use uv as python package manager.