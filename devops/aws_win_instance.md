# Deploying a Private Windows EC2 with Session Manager Access and Auto-Stop

## Architecture Overview

- **Private Subnet:** The Windows Server EC2 instance is launched in a private subnet with no public IP address or inbound access. It cannot be reached directly from the internet, enhancing security.
- **Session Manager RDP Access:** Instead of opening RDP (TCP 3389) to the world, AWS Systems Manager Session Manager is used to tunnel RDP via port forwarding, so you can connect securely without any inbound security group rules. The SSM Agent on the instance initiates outbound connections to AWS SSM endpoints.
- **SSM Connectivity:** The instance needs outbound network access to AWS SSM service. This can be achieved either by a NAT Gateway in a public subnet or by VPC Interface Endpoints for SSM (for services `ssm`, `ssmmessages`, and `ec2messages`) so that the SSM Agent can reach the Systems Manager service without a public internet connection. In this design, we illustrate using a NAT Gateway (alternatively, you could create the SSM VPC Endpoints to avoid needing a NAT).
- **IAM Role for SSM:** The EC2 instance is attached to an IAM role with the `AmazonSSMManagedInstanceCore` policy, allowing it to register with Systems Manager and enabling Session Manager connections. No SSH keys are needed for Session Manager access.
- **No Inbound Security Group Rules:** The security group for the instance is configured with no inbound ports open, as RDP access is done via Session Manager tunneling (which requires no inbound listener on the instance).
- **CloudWatch Idle Shutdown:** A CloudWatch Alarm monitors the instance's CPU usage. If CPU remains low (idle) for a defined period, the alarm triggers an automatic stop action on the EC2 instance to save costs. This uses CloudWatch alarm actions to stop the instance when idle, which AWS performs via a special service-linked role.

---

## Architecture Diagram

```mermaid
flowchart LR
  subgraph "AWS VPC"
    direction TB
    VPC[("VPC")]
    PublicSubnet[("Public Subnet")]
    PrivateSubnet[("Private Subnet")]
    EC2["EC2 Windows Server<br/>(no public IP)"]
    SG["Security Group:<br/>No Inbound Allowed"]
    NAT["NAT Gateway"]
    EC2 -- uses --> SG
    EC2 --> PrivateSubnet
    NAT --> PublicSubnet
    PublicSubnet -->|"IGW"| VPC
    PrivateSubnet --> VPC
  end
  EC2 ==> NAT
  NAT -- outbound--> SSM["AWS Systems Manager<br/>Service"]
  NAT -- outbound--> CloudWatch[("Amazon CloudWatch")]
  CloudWatch -."Stop Instance Action".-> EC2
  User(Client) -."Session Manager (RDP tunnel)".-> SSM
```

**Diagram Explanation:**

The EC2 instance resides in a private subnet (no direct internet). Outbound traffic (for SSM and CloudWatch) flows via a NAT Gateway (or via VPC Endpoints if configured). An admin user can establish an RDP session by running a Session Manager port forwarding command, which tunnels RDP through the SSM service to the instance. CloudWatch monitors the instance's CPU; if it stays below threshold for the set duration, CloudWatch (via an alarm action) stops the instance to save costs.

---

## Terraform Module Snippets

Below are Terraform module configurations split into reusable components: networking, security, IAM, EC2 instance, and monitoring. These snippets use placeholder variables (e.g., `var.region`, `var.name`) for customization (such as naming and region). Each module can be integrated into a larger Terraform project by exporting outputs (like VPC ID, subnet IDs, security group ID, etc.) and passing them to other modules. All resources include tags (like Name) for clarity.

### VPC and Networking Module

This module sets up the network: a VPC with a public and a private subnet, an Internet Gateway, and a NAT Gateway for outbound access. The private subnet's default route points to the NAT, enabling instances to reach the SSM service without being publicly accessible. (If you prefer not to use a NAT Gateway, you could instead create VPC Endpoints for SSM services within the private subnet.)

```hcl
# VPC with a /16 CIDR
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr           # e.g., "10.0.0.0/16"
  enable_dns_support   = true
  enable_dns_hostnames = true
  tags = {
    Name = "${var.name}-vpc"
  }
}

# Public subnet (for NAT Gateway)
resource "aws_subnet" "public" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidr  # e.g., "********/24"
  map_public_ip_on_launch = true
  availability_zone       = var.public_subnet_az    # e.g., "us-east-1a"
  tags = {
    Name = "${var.name}-public-subnet"
  }
}

# Private subnet (for EC2 instance)
resource "aws_subnet" "private" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_subnet_cidr  # e.g., "********/24"
  map_public_ip_on_launch = false
  availability_zone       = var.private_subnet_az    # e.g., "us-east-1a"
  tags = {
    Name = "${var.name}-private-subnet"
  }
}

# Internet Gateway for outbound internet access (for NAT)
resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.main.id
  tags = {
    Name = "${var.name}-igw"
  }
}

# NAT Gateway in the public subnet (for private subnet egress)
resource "aws_eip" "nat_eip" {
  vpc = true  # allocates an Elastic IP for the NAT Gateway
  tags = {
    Name = "${var.name}-nat-eip"
  }
}
resource "aws_nat_gateway" "nat" {
  allocation_id = aws_eip.nat_eip.id
  subnet_id     = aws_subnet.public.id
  depends_on    = [aws_internet_gateway.igw]
  tags = {
    Name = "${var.name}-nat-gateway"
  }
}

# Route table for public subnet (default route to IGW)
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }
  tags = { Name = "${var.name}-public-rt" }
}
resource "aws_route_table_association" "pub_assoc" {
  subnet_id      = aws_subnet.public.id
  route_table_id = aws_route_table.public.id
}

# Route table for private subnet (default route to NAT)
resource "aws_route_table" "private" {
  vpc_id = aws_vpc.main.id
  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat.id
  }
  tags = { Name = "${var.name}-private-rt" }
}
resource "aws_route_table_association" "priv_assoc" {
  subnet_id      = aws_subnet.private.id
  route_table_id = aws_route_table.private.id
}

# (Optional) If not using NAT, create SSM VPC Endpoints instead:
# resource "aws_vpc_endpoint" "ssm" {
#   vpc_id       = aws_vpc.main.id
#   service_name = "com.amazonaws.${var.region}.ssm"
#   subnet_ids   = [aws_subnet.private.id]        # for interface endpoint
#   security_group_ids = [aws_security_group.ssm_endpoints.id]  # security group for endpoint
#   private_dns_enabled = true
#   tags = { Name = "${var.name}-ssm-endpoint" }
# }
# (Repeat for "ssmmessages" and "ec2messages" endpoints as needed)
```

**Notes:**

- The above creates a NAT Gateway (with an Elastic IP) in the public subnet. The private subnet's route table sends all outbound traffic to the NAT, allowing the private instance to reach the SSM service and other AWS services.
- If using VPC Endpoints for SSM instead of NAT, you would create interface endpoints for `ssm`, `ssmmessages`, and `ec2messages` in the private subnet (and ensure the instance's security group allows outbound HTTPS to those endpoints). This would let the SSM Agent connect to Systems Manager privately.

---

### Security Group Module

This module defines a security group for the Windows instance with no inbound access allowed. All ingress is denied (empty rules), and only outbound traffic is permitted (default egress allows all outbound to the internet/other AWS services). This setup ensures no direct connections (like RDP) can reach the instance externally – RDP will instead be done through Session Manager's tunnel.

```hcl
resource "aws_security_group" "windows" {
  name        = "${var.name}-windows-sg"
  description = "Security group for private Windows server (no inbound access)"
  vpc_id      = var.vpc_id    # VPC where the instance resides

  ingress = []  # No inbound rules (all inbound traffic blocked)

  egress = [
    {
      from_port   = 0,
      to_port     = 0,
      protocol    = "-1",
      cidr_blocks = ["0.0.0.0/0"]   # allow all outbound
    }
  ]

  tags = { Name = "${var.name}-windows-sg" }
}
```

The security group has no ingress rules, meaning even RDP (TCP 3389) is not open. This is intentional: AWS SSM Session Manager will be used for RDP access via port forwarding, eliminating the need to open the RDP port. If internal access was needed (e.g., RDP from a bastion host), you could add an ingress rule for TCP 3389 from that source, but here we keep it fully closed.

---

### IAM Role Module (SSM Access)

This module creates an IAM role (and instance profile) for the EC2 instance, granting it permissions to communicate with Systems Manager. It uses the AWS-managed policy `AmazonSSMManagedInstanceCore`, which includes the necessary permissions for the SSM Agent (SSM Session Manager, Patch, etc.). The role is assumable by EC2 instances.

```hcl
# IAM role for EC2 to use Systems Manager (SSM)
resource "aws_iam_role" "ec2_ssm" {
  name = "${var.name}-ec2-ssm-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect    = "Allow",
      Principal = { Service = "ec2.amazonaws.com" },
      Action    = "sts:AssumeRole"
    }]
  })
  tags = { Name = "${var.name}-ec2-ssm-role" }
}

# Attach the AmazonSSMManagedInstanceCore managed policy for SSM access
resource "aws_iam_role_policy_attachment" "ssm_core" {
  role       = aws_iam_role.ec2_ssm.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# Instance Profile for the EC2 instance to use this role
resource "aws_iam_instance_profile" "ec2_ssm_profile" {
  name = "${var.name}-ec2-ssm-profile"
  role = aws_iam_role.ec2_ssm.name
}
```

This role allows the Windows EC2 to register with AWS Systems Manager. The `AmazonSSMManagedInstanceCore` policy includes permissions for Systems Manager agent to function (SSM Session Manager, sending metrics/heartbeats, etc.). By attaching the instance profile to the EC2, the SSM Agent on the instance can authenticate to AWS and you can start SSM sessions (such as port-forwarding sessions for RDP) without any additional credentials.

---

### EC2 Instance Module (Windows Server)

This module launches the Windows Server EC2 instance (e.g., Windows Server 2019 or 2022) in the private subnet. It uses the above components: the private subnet ID, the security group, and the IAM instance profile. The instance is configured without a public IP and with a custom EBS volume setup. We use an SSM Parameter to get the latest Windows AMI for the chosen version.

```hcl
# Lookup the latest Windows Server AMI (using AWS Systems Manager Parameter Store)
data "aws_ssm_parameter" "windows_ami" {
  name = "/aws/service/ami-windows-latest/Windows_Server-2019-English-Full-Base"
  # For Windows Server 2022, use "Windows_Server-2022-English-Full-Base"
}

resource "aws_instance" "windows" {
  ami                    = data.aws_ssm_parameter.windows_ami.value
  instance_type          = var.instance_type         # e.g., "t3.medium"
  subnet_id              = var.subnet_id             # private subnet for the instance
  vpc_security_group_ids = [var.security_group_id]   # attach the SG with no inbound access
  associate_public_ip_address = false                # ensure no public IP

  iam_instance_profile   = var.instance_profile_name # use IAM role for SSM access

  key_name       = var.key_name  # (optional) key pair name, for initial Windows password retrieval
  # Note: A key pair is optional if you exclusively use SSM. If provided, it can decrypt the default admin password.

  root_block_device {
    volume_size = var.root_volume_size   # e.g., 30 (GiB)
    volume_type = var.root_volume_type   # e.g., "gp3"
    delete_on_termination = true
  }

  tags = { 
    Name = "${var.name}-windows-instance",
    Role = "PrivateWindowsServer"
  }
}
```

In this configuration, the Windows instance is private (no public IP) and only reachable via Systems Manager Session Manager. The `associate_public_ip_address = false` and placement in a private subnet ensure the instance isn't exposed to the internet. The SSM Agent comes pre-installed on AWS Windows AMIs, so once the instance starts, it should appear in Systems Manager. The `aws_ssm_parameter` data source fetches the latest Amazon Machine Image for Windows Server 2019 – you can change this to 2022 as needed. We attach the IAM instance profile for SSM, and we optionally specify a key pair (if you want to retrieve the Administrator password via AWS console or CLI). The root EBS volume is set to use gp3 (for better baseline performance) and sized via a variable. Additional EBS volumes or configuration can be added as needed.

---

### CloudWatch Alarm Module (Idle Shutdown)

This module creates a CloudWatch Metric Alarm to automatically stop the EC2 instance when it's idle (low CPU utilization) for a defined time. For example, below we configure the alarm to trigger if average CPU stays below 5% for 15 minutes (3 periods of 5 minutes each) – indicating the server is likely idle. When triggered, the alarm will invoke the EC2 Stop action on the instance, shutting it down to save costs. (You can adjust the threshold or period to suit your needs.)

```hcl
# CloudWatch alarm to stop the instance when CPU is low (idle) for a while
resource "aws_cloudwatch_metric_alarm" "idle_stop" {
  alarm_name          = "${var.name}-idle-stop-alarm"
  alarm_description   = "Auto-stop the instance when CPU is low for 15 minutes (idle)."
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = 5                           # 5% CPU utilization threshold
  period              = 300                         # 5 minutes interval
  statistic           = "Average"
  evaluation_periods  = 3                           # 3*5min = 15 minutes of low utilization
  datapoints_to_alarm = 3                           # require all 3 consecutive periods to be low

  metric_name = "CPUUtilization"
  namespace   = "AWS/EC2"
  dimensions  = { InstanceId = var.instance_id }    # target instance to monitor

  treat_missing_data = "missing"   # treat missing data as missing (so stopped instance doesn't trigger alarm)
  alarm_actions      = ["arn:aws:automate:${var.region}:ec2:stop"]
  # The special ARN above triggers the EC2 Stop action in this region
}
```

When the average CPU stays below 5% for 3 consecutive 5-min periods (15 minutes total), this alarm goes into ALARM state. The `alarm_actions` uses the AWS predefined action `arn:aws:automate:<region>:ec2:stop` to stop the specific instance. AWS CloudWatch will use a service-linked role (`AWSServiceRoleForCloudWatchEvents`) to execute the stop on your behalf. You could also add an SNS notification in `alarm_actions` (or `ok_actions`) to get alerted when this happens, but here we focus on the stop action. By stopping idle instances automatically, you ensure you don't pay for compute time you're not using, and you can easily restart the instance later (retaining the same data and configuration).

**Make sure the instance is EBS-backed (not instance-store) so that it can be stopped and later started again** (most Amazon Machine Images including Windows are EBS-backed). The above configuration assumes you pass in the `instance_id` (for the EC2 to monitor) and `var.region` (AWS region) to construct the alarm ARN.

---

## Optional Python Script – Start Instance on Demand

When the CloudWatch alarm stops the instance due to low activity, you'll need to start it again when you want to use it next. This can be done via the AWS Console or CLI, or automated. Below is an optional Python snippet using `boto3` that finds the instance by ID and starts it. This could be used in a scheduled job or triggered manually to spin up the server when needed.

```python
import boto3

ec2 = boto3.client('ec2')  # uses AWS credentials & default region from environment
instance_id = "i-0123456789abcdef0"  # replace with your instance ID

try:
    ec2.start_instances(InstanceIds=[instance_id])
    print(f"Started instance {instance_id}")
except Exception as e:
    print(f"Error starting instance: {e}")
```

This script utilizes the EC2 `start_instances` API to initiate the instance start. Ensure your AWS credentials and region are configured (e.g., via environment or AWS config file). You could extend this script to wait for the instance to be running and even establish a Session Manager port-forward automatically. When combined with the automatic stop alarm, this on-demand start approach helps minimize costs while still allowing easy access to the server when required.

---

## Sources

1. AWS Systems Manager documentation on Session Manager port forwarding confirms that a private instance with no inbound access can be reached via an SSM tunnel for RDP. The instance must either have NAT Gateway access or VPC Endpoints for SSM to allow the SSM Agent's outbound connection, and it needs the IAM role with the `AmazonSSMManagedInstanceCore` policy for permission to communicate with the Systems Manager service.
2. AWS CloudWatch documentation on alarm actions notes that you can configure an alarm to stop an EC2 instance when, for example, CPU utilization remains below a threshold for a certain time, indicating the instance is idle. The Terraform snippet uses the alarm action ARN (`...:ec2:stop`) as shown in similar implementations. AWS will use a service-linked role to perform the stop action on the instance when the alarm triggers. This setup ensures the Windows server automatically shuts down when not in use, and can be restarted on demand to save costs.