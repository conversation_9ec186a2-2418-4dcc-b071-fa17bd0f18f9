Secure, Verifiable macOS Backup – Implementation Plan


1 · Project Goals

Goal	Details
Space‑efficient & browsable archive	Compress user data while retaining the ability to list contents and extract individual files without unpacking everything.
Data integrity	Verify each backup immediately after creation (test read + SHA‑256 checksum).
Confidentiality	Encrypt backups with strong, authenticated encryption (default: GnuPG AES‑256, passphrase‑protected).
Off‑site storage	Upload the encrypted archive to Amazon S3 with selectable storage class, server‑side encryption, and lifecycle tags.
Granular restore	Support restoring a single file or directory via the archive’s built‑in index plus streaming decryption.
Automation & re‑use	All runtime choices come from a settings file; scripts are fully logged, non‑interactive when scheduled, and modular for maintenance.


⸻

2 · Chosen Toolchain

Task	Tool	Notes
Archiving	DAR (dar)	Indexed catalog ➜ instant content listing & single‑file extraction. Preserves macOS metadata (ACLs, resource forks). Homebrew package: brew install dar.
Compression algorithm	Gzip (default) or zstd	DAR compresses files individually; swap -Z gzip for -Z zstd if zstd is installed.
Integrity test	dar -t + shasum -a 256	Reads entire archive; exits non‑zero on error.
Encryption	GPG (gpg -c)	Symmetric AES‑256, modern KDF & integrity check (MDC). Alternative: age binary if preferred.
Cloud upload	AWS CLI (aws s3 cp)	Supports storage class flag --storage-class, SSE (--sse AES256 or --sse aws:kms).
Optional orchestration	Python 3 + boto3	For richer progress, multipart tuning, or tagging at upload time; not required for MVP.


⸻

3 · Directory Layout

mac-backup/
├── backup.conf          # user‑editable settings
├── backup.sh            # main Bash script
├── restore.sh           # helper script (optional)
└── logs/                # timestamped run logs


⸻

4 · Sample backup.conf

##########################################
# Backup settings – edit as required
##########################################

# Source directories (space‑separated)
SOURCE_DIRS=( "/Users/<USER>/Documents" "/Users/<USER>/Pictures" )

# Exclude glob patterns
EXCLUDE_PATTERNS=( "*.git" "node_modules" "*.tmp" )

# Archive base‑name (script adds date)
BACKUP_NAME="macbookpro_backup"

# Compression method: dar | zip
COMPRESSION_METHOD="dar"

# Encryption – path to file containing passphrase
ENCRYPTION_PASSWORD_FILE="$HOME/.backup_pass"

# AWS / S3
AWS_PROFILE="personal"
S3_BUCKET="my-backups"
S3_PREFIX="laptop1/"              # optional path inside bucket
S3_STORAGE_CLASS="STANDARD_IA"    # or GLACIER, DEEP_ARCHIVE, etc.
S3_SSE="AES256"                   # "AES256" | "aws:kms" | "" (none)
S3_KMS_KEY_ID=""                  # if using KMS

# Optional object tags (KEY=VALUE[,KEY2=VALUE2...])
S3_TAGS="Frequency=Weekly,Platform=macOS"


⸻

5 · Core Bash Script (backup.sh)

#!/bin/bash
set -euo pipefail

# ── 0. Bootstrap ─────────────────────────────────────────────────────────────────
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/backup.conf"

LOG_DIR="$SCRIPT_DIR/logs"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/backup-$(date +%Y%m%d-%H%M).log"
exec > >(tee -a "$LOG_FILE") 2>&1

echo "=== Backup started at $(date) ==="

# ── 1. Compression ──────────────────────────────────────────────────────────────
TODAY="$(date +%Y%m%d)"
ARCHIVE="${BACKUP_NAME}-${TODAY}.dar"
echo "[1] Creating DAR archive $ARCHIVE …"

EXCL=()
for pat in "${EXCLUDE_PATTERNS[@]}"; do EXCL+=( "--exclude=$pat" ); done

dar -c "$ARCHIVE" -R "${SOURCE_DIRS[0]}" "${EXCL[@]}"
echo "Archive created."

# ── 2. Verification ─────────────────────────────────────────────────────────────
echo "[2] Verifying archive integrity …"
dar -t "$ARCHIVE"
shasum -a 256 "$ARCHIVE" > "${ARCHIVE}.sha256"
echo "Verification passed."

# ── 3. Encryption ───────────────────────────────────────────────────────────────
ENC_FILE="${ARCHIVE}.gpg"
if [[ -f "$ENCRYPTION_PASSWORD_FILE" ]]; then
    ENC_PASS=$(<"$ENCRYPTION_PASSWORD_FILE")
else
    read -s -p "Enter encryption passphrase: " ENC_PASS && echo
fi
echo "$ENC_PASS" | gpg --batch --yes --passphrase-fd 0 -c "$ARCHIVE"
unset ENC_PASS
rm -f "$ARCHIVE"
echo "Encrypted ➜ $ENC_FILE"

# ── 4. Upload to S3 ─────────────────────────────────────────────────────────────
S3_URI="s3://${S3_BUCKET}/${S3_PREFIX}${ENC_FILE}"
AWS_ARGS=( )
[[ -n "${AWS_PROFILE:-}"    ]] && AWS_ARGS+=( --profile "$AWS_PROFILE" )
[[ -n "${S3_STORAGE_CLASS}" ]] && AWS_ARGS+=( --storage-class "$S3_STORAGE_CLASS" )
if [[ "$S3_SSE" == "AES256" ]]; then
    AWS_ARGS+=( --sse AES256 )
elif [[ "$S3_SSE" == "aws:kms" ]]; then
    AWS_ARGS+=( --sse aws:kms )
    [[ -n "$S3_KMS_KEY_ID" ]] && AWS_ARGS+=( --sse-kms-key-id "$S3_KMS_KEY_ID" )
fi

echo "[4] Uploading to $S3_URI …"
aws s3 cp "$ENC_FILE" "$S3_URI" "${AWS_ARGS[@]}"

# Apply tags after upload
if [[ -n "${S3_TAGS:-}" ]]; then
    echo "[4b] Tagging object …"
    aws s3api put-object-tagging --bucket "$S3_BUCKET" \
         --key "${S3_PREFIX}${ENC_FILE}" \
         --tagging "TagSet=$(IFS=','; for kv in $S3_TAGS; do IFS='=' read k v <<< "$kv"; printf '[{Key=%s,Value=%s}]' "$k" "$v"; done)"
fi
echo "Upload complete."

# ── 5. Cleanup ──────────────────────────────────────────────────────────────────
# Optionally remove encrypted file after confirmed upload
# rm -f "$ENC_FILE"

echo "=== Backup finished at $(date) ==="

Notes
	•	Requires: brew install dar gpg2 awscli (and, optionally, zstd).
	•	Script runs unattended if all variables are filled in backup.conf.
	•	Schedule via launchd (macOS) or cron, e.g.
0 2 * * * /path/mac-backup/backup.sh >/dev/null 2>&1.

⸻

6 · Restore Helper (restore.sh – optional)

#!/bin/bash
# Usage examples:
#   restore.sh latest /tmp/restore              # full restore
#   restore.sh 20250709 /tmp/restore 'Documents/report.pdf'   # single file

set -euo pipefail
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/backup.conf"

DATE=${1:-latest}
DEST=${2:-"$PWD"}
TARGET=${3:-""}          # optional file/dir inside archive

# Resolve backup name (latest vs specific YYYYMMDD)
if [[ "$DATE" == "latest" ]]; then
    OBJ=$(aws s3 ls "s3://${S3_BUCKET}/${S3_PREFIX}" | grep "$BACKUP_NAME" | sort | tail -1 | awk '{print $4}')
else
    OBJ="${BACKUP_NAME}-${DATE}.dar.gpg"
fi

echo "Downloading $OBJ …"
aws s3 cp "s3://${S3_BUCKET}/${S3_PREFIX}${OBJ}" - | \
    gpg -d | \
    if [[ -z "$TARGET" ]]; then
        dar -x -R "$DEST" -  # full restore
    else
        dar -x -R "$DEST" -g "$TARGET" -  # partial restore
    fi
echo "Restore complete ➜ $DEST"


⸻

7 · AWS S3 Settings Reference

Option	Config Variable	Example	Impact
Storage class	S3_STORAGE_CLASS	STANDARD_IA / GLACIER	Lower cost vs retrieval time.
Server‑side encryption	S3_SSE	AES256 (SSE‑S3) or aws:kms	Adds at‑rest encryption by AWS.
KMS key	S3_KMS_KEY_ID	arn:aws:kms:…:key/abc…	Use custom CMK; required if S3_SSE=aws:kms.
Object tags	S3_TAGS	Frequency=Weekly	Drives lifecycle policies (e.g., auto‑delete).


⸻

8 · Logging & Monitoring
	•	Local logs: Each run writes logs/backup-YYYYMMDD-HHMM.log.
	•	CloudTrail / S3 access logs confirm successful uploads and downloads.
	•	Integrate with monitoring (e.g., Datadog, CloudWatch) by tailing exit codes or parsing logs for “Upload complete” / “Verification passed”.
	•	Optionally send a Slack / Teams alert on failure.

⸻

9 · Security Guidelines
	1.	Passphrase storage – Keep ~/.backup_pass readable only by the backup user (chmod 600).
	2.	IAM least privilege – AWS profile needs only s3:PutObject, s3:GetObject, s3:PutObjectTagging on the backup prefix.
	3.	Mac permissions – Run script under a dedicated user or via launchd RunAtLoad with minimal privileges.
	4.	Plain archive deletion – Script removes the unencrypted .dar after successful encryption to avoid residual plaintext.
	5.	Key rotation – Change GPG passphrase periodically and re‑encrypt older archives as policy dictates.

⸻

10 · Scheduling

Scheduler	Example
Cron	0 2 * * * /usr/local/bin/backup.sh >/dev/null 2>&1
launchd (macOS)	Create com.company.backup.plist in ~/Library/LaunchAgents calling backup.sh; set StartCalendarInterval for desired time.


⸻

11 · Next Steps for IT Admin / DevOps
	1.	Install prerequisites – brew install dar gpg2 awscli (plus age / zstd if needed).
	2.	Create backup.conf – Fill in directories, excludes, S3 details, encryption file.
	3.	Run backup.sh manually – Verify archive creation, integrity test, encryption, and S3 upload succeed.
	4.	Test restore – Use restore.sh for both full and single‑file recovery; confirm file integrity.
	5.	Automate – Add cron/launchd schedule, ensure logs are rotated or shipped to monitoring.
	6.	Document – Store run‑book with passphrase location, AWS profile name, and restore commands for quick disaster recovery.

⸻