# Deployment Specification

## EC2 Spot + EBS "Spin-Up-on-Demand" Inference Environment

*(Implementation guide for DevOps teams using Terraform and lightweight Python automation)*

---

## 1. Overview

| Goal | Detail |
|------|--------|
| **Workload** | Ad-hoc LLM inference (7 – 30 B parameters) |
| **Key design points** | • 99% cost saving from Spot Instances<br>• Persist model weights on encrypted gp3 EBS so they survive re-launches<br>• Auto-Scaling Group (ASG) with min = 0, max = 1 gives "scale-to-zero"<br>• ASG Lifecycle hooks + small Lambda/Python attach & detach the data volume<br>• CloudWatch alarms shut the instance after X minutes idle (≤ 10% CPU)<br>• All resources declared in Terraform modules for repeatability |

---

## 2. High-Level Architecture

```mermaid
graph TD
    subgraph Account VPC
        ALB[Optional API / Bastion] -->|scale-up event| EB[EventBridge Rule]
        EB --> ASG[Auto Scaling Group<br/>min 0 → max 1]
        ASG -->|Launch Template| EC2[(EC2 Spot<br/>g5.xlarge)]
        EC2 -- user-data mount --> VOL[(EBS gp3 100 GB<br/>HuggingFace cache)]
        ASG -->|Launch hook| L1[Lambda Attach<br/>boto3 attach_volume]
        EC2 -.->|Interruption Notice| L2[Lambda Detach & Snapshot]
        EC2 --CW Logs--> CW[(CloudWatch Logs/Alarms)]
        CW --> EBStop[EventBridge Idle Stop rule]
        EBStop --> ASG
        SNS((SNS)) --> PagerDuty
        L2 --> SNS
    end
```

**Legend:**
- **Blue nodes** = compute
- **Grey** = storage  
- **Green** = automation / control plane

---

## 3. Terraform Module Layout

```
live/
 ├─ prod/
 │   ├─ main.tf
 │   ├─ variables.tf
 │   └─ terraform.tfvars
 modules/
 ├─ vpc/
 ├─ security-group/
 ├─ iam/
 │   ├─ roles.tf      # EC2 instance profile, Lambda role, ASG service-linked role
 ├─ ebs-data/
 │   ├─ main.tf       # Encrypted gp3 volume, 100–256 GB, DeleteOnTermination=false
 ├─ launch-template/
 │   ├─ user_data.tpl # cloud-init that mounts /mnt/models and sets TRANSFORMERS_CACHE
 ├─ asg-spot/
 │   ├─ lifecycle.tf  # launch/terminate hooks to SNS
 │   └─ autoscaling.tf
 ├─ lambda-hooks/
 │   └─ src/attach_detach.py
 ├─ cloudwatch/
 │   ├─ alarms.tf     # Idle-CPU alarm, spot interruption alarm
 │   └─ eventbridge.tf
 └─ outputs.tf
```

> **Tip:** Keep stateful bits (EBS, AMIs) in their own modules so you can destroy the ASG without losing model data.

### 3.1 Core Terraform snippets

<details>
<summary><strong>Launch template (g5.xlarge Spot)</strong></summary>

```hcl
resource "aws_launch_template" "llm_spot" {
  name_prefix        = "llm-spot-"
  image_id           = data.aws_ami.ubuntu_gpu.id
  instance_type      = var.instance_type # e.g. g5.xlarge
  key_name           = var.key_pair
  user_data          = base64encode(templatefile("${path.module}/user_data.tpl", {
                          volume_device = var.device_name,       # /dev/xvdf
                          mount_path    = "/mnt/models"
                        }))
  iam_instance_profile {
    name = aws_iam_instance_profile.ec2_profile.name
  }
  metadata_options {
    http_tokens = "required"
  }
  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "llm-spot"
      Project = var.project
    }
  }

  instance_market_options {
    market_type = "spot"
    spot_options {
      max_price            = "0.60"     # ≈60% below on-demand cap
      spot_instance_type   = "one-time"
      instance_interruption_behavior = "terminate"
    }
  }
}
```

</details>

<details>
<summary><strong>Auto Scaling Group (0 ↔ 1)</strong></summary>

```hcl
resource "aws_autoscaling_group" "llm_asg" {
  name               = "llm-asg"
  max_size           = 1
  min_size           = 0
  desired_capacity   = 0
  vpc_zone_identifier= data.aws_subnets.private.ids
  launch_template {
    id      = aws_launch_template.llm_spot.id
    version = "$Latest"
  }

  lifecycle {
    create_before_destroy = true
  }

  # Spot interruption lifecycle hook
  lifecycle_hook {
    name                   = "DETACH_ON_TERMINATE"
    default_result         = "CONTINUE"
    heartbeat_timeout      = 300
    lifecycle_transition   = "autoscaling:EC2_INSTANCE_TERMINATING"
    notification_target_arn= aws_sns_topic.asg_hooks.arn
    role_arn               = aws_iam_role.asg_hook_role.arn
  }

  # Launch hook – attach EBS
  lifecycle_hook {
    name                 = "ATTACH_ON_LAUNCH"
    default_result       = "CONTINUE"
    lifecycle_transition = "autoscaling:EC2_INSTANCE_LAUNCHING"
    notification_target_arn = aws_sns_topic.asg_hooks.arn
    role_arn             = aws_iam_role.asg_hook_role.arn
  }
}
```

</details>

<details>
<summary><strong>EBS volume and data module</strong></summary>

```hcl
resource "aws_ebs_volume" "model_cache" {
  availability_zone = var.az
  size              = var.volume_size_gb         # 100‑256 GB
  type              = "gp3"
  encrypted         = true
  tags = {
    Name = "llm-model-cache"
    Project = var.project
  }
}

# Optional nightly snapshot
resource "aws_backup_plan" "daily_snap" {
  name = "llm-daily"
  rule {
    rule_name         = "DailyBackups"
    schedule          = "cron(0 5 * * ? *)"
    target_vault_name = aws_backup_vault.vault.name
    lifecycle {
      delete_after = 30
    }
  }
  selection {
    name = "llm-ebs"
    resources = [aws_ebs_volume.model_cache.arn]
    iam_role_arn = aws_iam_role.backup_role.arn
  }
}
```

</details>

---

## 4. Python Automation (Lifecycle Hooks)

### `attach_detach.py`

```python
#!/usr/bin/env python3
"""
Lambda function subscribed to the ASG SNS topic.  
 - On 'launching': attaches the pre‑provisioned EBS volume and waits
   for /dev/xvdf to become 'in‑service'.
 - On 'terminating' OR Spot interruption: unmounts, detaches, and (optionally)
   snapshots the volume, then signals CONTINUE to the ASG lifecycle hook.
"""
import boto3, json, os, time

ec2   = boto3.client("ec2")
asg   = boto3.client("autoscaling")
REGION= os.environ["AWS_REGION"]
VOL_ID= os.environ["EBS_VOLUME_ID"]

def lambda_handler(event, _ctx):
    msg  = json.loads(event["Records"][0]["Sns"]["Message"])
    hook = msg["LifecycleHookName"]
    inst = msg["EC2InstanceId"]
    token= msg["LifecycleActionToken"]
    asg_name = msg["AutoScalingGroupName"]

    if "LAUNCHING" in msg["LifecycleTransition"]:
        device = "/dev/xvdf"
        print(f"Attaching {VOL_ID} to {inst}")
        ec2.attach_volume(Device=device, InstanceId=inst, VolumeId=VOL_ID)
        waiter = ec2.get_waiter("volume_in_use")
        waiter.wait(VolumeIds=[VOL_ID])
        # (optional) SSM run‑command to mount & chown
        _complete_lifecycle(asg_name, hook, token)
    elif "TERMINATING" in msg["LifecycleTransition"]:
        print(f"Detaching {VOL_ID} from {inst}")
        ec2.detach_volume(InstanceId=inst, VolumeId=VOL_ID, Force=True)
        waiter = ec2.get_waiter("volume_available")
        waiter.wait(VolumeIds=[VOL_ID])
        # snapshot for DR
        snap = ec2.create_snapshot(VolumeId=VOL_ID, Description="LLM cache")
        print("Snapshot", snap["SnapshotId"])
        _complete_lifecycle(asg_name, hook, token)

def _complete_lifecycle(asg_name, hook, token):
    asg.complete_lifecycle_action(
        AutoScalingGroupName=asg_name,
        LifecycleHookName=hook,
        LifecycleActionResult="CONTINUE",
        LifecycleActionToken=token,
    )
```

### User-data `user_data.tpl` (excerpt)

```bash
#!/bin/bash
set -xe
DEVICE="${volume_device}"
MNT="${mount_path}"

# Wait for device to appear
while [ ! -e $DEVICE ]; do sleep 1; done

mkdir -p $MNT
mount $DEVICE $MNT
chmod 777 $MNT

echo "$DEVICE $MNT ext4 defaults,nofail 0 2" >> /etc/fstab

export TRANSFORMERS_CACHE=$MNT/hf-cache
```

**Why Lambda vs. userdata?**
- Launch-time Lambda can re‑use an already existing EBS volume across any new instance.
- Termination hook ensures clean detach even when the instance is forcibly reclaimed by Spot.

---

## 5. Security & Compliance

| Layer | Design |
|-------|--------|
| **IAM** | • EC2 instance profile – least‑priv: `ec2:Describe*`, `ec2:AttachVolume`, `s3:GetObject` (only if pulling from S3), `CloudWatch PutMetricData`<br>• Lambda role – `ec2:AttachVolume/DetachVolume`, `autoscaling:CompleteLifecycleAction`, `ec2:CreateSnapshot` (if enabled)<br>• ASG service‑linked role – auto‑created<br>• Use distinct customer‑managed policy, attach only required actions |
| **Network** | • Place EC2 in private subnets (no IGW) behind NAT if outbound needed<br>• Security group ingress limited to Jump/ALB CIDRs (SSH or HTTPS API only)<br>• VPC endpoint for ECR, S3 to avoid public internet when pulling containers or models |
| **Secrets** | • Use AWS Systems Manager Parameter Store for any API keys<br>• Pass to instance via `aws ssm get-parameter --with-decryption` in user‑data |
| **EBS Encryption** | • Set `encrypted = true` with KMS CMK<br>• Enable Fast Snapshot Restore if snapshotting for DR |
| **Compliance checks** | • Enable AWS Config rules: `encrypted-volumes`, `restricted-ssh`, `iam-user-no-policies` |

---

## 6. Monitoring, Logging & Idle Shutdown

| Concern | Implementation |
|---------|----------------|
| **System metrics** | AWS/EC2 CPU Utilization, GPUUtilization (via NVML → CloudWatch Agent) |
| **Idle alarm** | `AlarmIdleLowCPU` → triggers EventBridge → `aws autoscaling set-desired-capacity 0` |
| **Spot notices** | Instance metadata / EC2 Spot Interruption Warning sends to CloudWatch Events; Lambda detaches volume & SNS alert |
| **Log aggregation** | CloudWatch Logs agent tailing `/var/log/user-data.log`, `application.log` |
| **Dashboards** | CloudWatch "LLM‑Inference" dashboard: GPU %, latency (app metric), instance lifecycle events |
| **Cost guardrails** | • Budgets with alert < $50/month<br>• Resource Explorer or AWS Config rule for `stopped-spot-with-volume` to detect orphaned EBS |

---

## 7. Operational Run‑Book

| Phase | Action |
|-------|--------|
| **First deployment** | `terraform init && terraform apply` → creates EBS volume (blank) |
| **Model seeding** | 1. Manually scale ASG to 1<br>2. SSH, `pip install torch transformers`, run `from_pretrained()` into `/mnt/models`<br>3. Stop ASG (desired 0). Volume now holds cache. |
| **Normal use** | – Trigger scale‑up (CLI, EventBridge, or ALB health check)<br>– Inference job runs; idle alarm shuts instance automatically<br>– On Spot reclaim, Lambda snapshots + detaches; ASG relaunches automatically. |
| **Upgrades** | • Change AMI ID in launch template version via Terraform<br>• Rolling replacement handled by `create_before_destroy` |
| **Disaster recovery** | • Restore latest snapshot to new volume, update `EBS_VOLUME_ID` Lambda env var<br>• Re‑apply Terraform, ASG spins up with restored data |

---

## 8. Parameterization & Default Values

| Variable | Default | Notes |
|----------|---------|-------|
| `instance_type` | `g5.xlarge` | 24 GB GPU; change to `p4d.24xlarge` for 30B FP16 |
| `max_price` | `"0.60"` | 60% of on‑demand; adjust per region |
| `volume_size_gb` | `100` | Enough for 30B model @4‑bit + cache |
| `idle_minutes` | `20` | CPU < 10% for this many minutes triggers stop |

All parameters exposed in `variables.tf` so platform teams can inject via CI/CD pipeline (e.g., Atlantis, Terraform Cloud, GitHub Actions).

---

## 9. Extensibility Pointers

1. **Multiple models** – create one EBS per model and tag with `ModelId`; modify Lambda to pick volume based on ASG tag (pass via SNS).
2. **Vertical scale** – expose `instance_type` list and use Instance Requirements in launch template to pick cheapest GPU with >= 24 GB VRAM.
3. **Horizontal scale** – set `max_size` > 1 and pin one EBS per AZ; use multi‑attach `io2` volumes if concurrent read‑only access is required.
4. **GPU cost control** – integrate EC2 Instance Scheduler or AWS Compute Optimizer to recommend cheaper Spot pools.

---

## 10. Summary Checklist

| ✅ | Item |
|----|------|
| | Terraform modules committed, code‑reviewed |
| | CI pipeline runs `tflint`, `tfsec`, `checkov` |
| | KMS CMK rotated annually |
| | Idle‑CPU alarm verified (simulate by `stress --cpu 0`) |
| | Spot interruption simulation (`aws ec2 simulate-spot-instance-interruption`) |
| | Lambda Attach/Detach function unit‑tested with `moto` |
| | Backup plan retention tested (restore snapshot) |

---

**Implement the above and you'll have a stateless, cost‑optimized, self‑healing inference platform that spins up on demand, persists heavyweight model artifacts, and costs (almost) nothing while idle.**