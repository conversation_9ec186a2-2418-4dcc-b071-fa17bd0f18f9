please add tests to cover all commands under main.py with different options. please make sure you include all the edge cases.
tests will create temp dir under tests and will use that temp dir for all operations.
after test run, please delete the temp dir.


# Bug and fix
please review the code in following files and look for potential bugs. 
only return the list of potential bugs and nothing else. please do not not look for validation checks, security vulnerability or performance issues.
only look for bugs that may lead to unexpected behavior or cause errors.




please fix the bugs listed in file below. file also contains suggested fix so use that information to apply fix to the code. 
deepseek_chat_review.md


# Refined prompt
Code Review for Functional Bugs
Instructions:

Analyze the provided codebase exclusively for functional bugs that could result in:

Runtime errors (exceptions, crashes)

Incorrect data processing/output

Unintended control flow (e.g., infinite loops, misplaced conditionals)

Exclude: Validation checks, security vulnerabilities, performance optimizations, and code style issues.

Output Format:

For each identified bug, provide:

File & Location (line/function name)

Bug Description (1–2 sentences explaining the unexpected behavior/error)

Fix Suggestion (specific code change or logic adjustment)

Return only the list of bugs and fixes. No summaries, headers, or explanations.


# User prompt
please review main.py commands and look for bugs. you can read the other files to understand the code.
please write the bugs to {model_name}_bugs.md file.