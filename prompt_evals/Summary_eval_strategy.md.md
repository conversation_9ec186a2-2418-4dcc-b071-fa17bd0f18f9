  

Evaluation Prompts and Metrics for LLM‑as‑a‑Judge in Airbnb Review Summarizations

  

  

  

Introduction

  

  

Large language models (LLMs) are increasingly used to summarize user‑generated content such as Airbnb reviews and to evaluate the quality of those summaries.  Human evaluation is costly and inconsistent, so LLM‑as‑a‑Judge frameworks have become popular for scalable, reproducible assessment.  However, recent research warns that these models are sensitive to various biases and can produce inconsistent judgments .  Fairness in this context involves more than avoiding explicit bias – it also requires tackling subtle forms of bias in the scoring process .  Effective evaluation must ensure that summaries are factually grounded, balanced across viewpoints, and directly tied to evidence in the reviews while being concise and relevant to user‑defined focus factors.

  

This document develops prompts and evaluation metrics for an LLM‑as‑a‑Judge system that assesses Airbnb review summarizations.  The goal is to create standardized summary prompts for generating summaries, design evaluation metrics that capture coverage, accuracy, faithfulness, balance, clarity, and relevance, and craft a judge prompt that produces consistent scores on a 0–5 scale with justifications and an aggregated overall score.  Each section below is grounded in current research on bias and summary evaluation.

  

  

Research Findings & Best Practices

  

  

  

Biases and limitations of LLM judges

  

  

LLM judges are not inherently objective.  Studies show that they exhibit non‑determinism, meaning the same output can receive different scores at different times .  They also demonstrate self‑preference (narcissistic) bias, where a model tends to rate its own outputs more favorably .  Length bias and verbosity preference are common: judges tend to favor more verbose answers over concise ones, which is problematic for summary evaluation .  Recent work further identifies positional bias and self‑enhancement bias, where altering the order of outputs or using the same model for generation and evaluation can skew scores .  To mitigate these issues, experts recommend using different models for generation and evaluation , keeping the scoring scale coarse (e.g., 0–5) to reduce random variability , and supplementing automated judgments with human review .

  

Fairness goes beyond eliminating overt prejudice; it must address subtle biases.  Fairness metrics evaluate whether the LLM delivers unbiased and equitable judgments across different demographics, writing styles, or data distributions .  Because LLMs trained on large corpora may inherit cultural or socioeconomic biases, prompts should explicitly instruct them to avoid subjective language and to represent minority opinions proportionally .

  

  

Summarization evaluation attributes

  

  

Recent work on perspective summarization identifies two core attributes for high‑quality summaries: coverage and faithfulness .  Coverage measures how well a summary includes all key content from the intended perspective, while faithfulness assesses whether the summary excludes statements unsupported by the source articles .  LLM‑based metrics (e.g., ALIGNSCORE) are found to be stronger evaluators than traditional n‑gram metrics .  The same study notes that reranking approaches using preference tuning improve faithfulness without sacrificing abstractiveness , highlighting the need for evaluators that reward factual fidelity rather than verbosity.

  

  

Human oversight and prompt design

  

  

LLM‑as‑a‑Judge frameworks should integrate human oversight because automated evaluation alone cannot guarantee fairness or accuracy .  Combining LLM judgment with human review yields more trusted evaluation pipelines and allows experts to verify scores and adjust prompts.  Prompt design plays a crucial role: instructions must clearly define evaluation criteria and encourage the judge to provide concise justifications.  The prompt should also ask for a separate justification to enhance explainability .  Using different models for generation and evaluation reduces self‑preference bias .

  

  

Summary Prompt Design

  

  

The summary prompt guides an LLM to read a set of Airbnb reviews and produce a concise property summary emphasizing specific focus factors (e.g., “Green Space,” “Tranquility,” “Minimalist Apartment”).  To ensure fairness and factuality, the prompt must instruct the model to:

  

1. Address every focus factor mentioned in the reviews; if a factor is absent, explicitly state this.
2. Ground statements in evidence from the reviews, avoiding hallucinations or unsupported claims.
3. Represent contradictory opinions proportionally, rather than ignoring minority viewpoints or overemphasizing rare mentions.
4. Maintain clarity and conciseness, avoiding unnecessary verbosity that might bias later evaluation .

  

  

Below is the standardized Summary Prompt to be used with LLMs:

You are given a list of reviews for an Airbnb property and a set of user‑defined focus factors (e.g., “Green Space,” “Tranquility,” “Minimalist Apartment”).

1. Read all of the reviews carefully.  For each focus factor, extract evidence from the reviews that pertains to that factor.  Evidence may include direct mentions, descriptions, and sentiments (positive or negative).
2. Write a concise summary (≈150–200 words) describing the property and how the reviews discuss each focus factor.  Use your own words, but base every statement on evidence from the reviews.
3. For each focus factor:
   • If the reviews mention it, summarize the sentiment and frequency of mentions (e.g., “Many guests praised the ample green space, while a few noted it needed maintenance”).
   • If the reviews contain conflicting opinions, represent them proportionally rather than ignoring minority views.
   • If the focus factor is not mentioned in any review, explicitly state that the reviews do not address it.
4. Do not invent details or draw on external knowledge.  Avoid speculative or subjective language.  Keep the summary clear, balanced, and free of verbosity.

Return only the summary (no explanations or lists).
This prompt explicitly instructs the summarizer to cover all focus factors, state when a factor is not present, and avoid hallucinations. By requiring proportional representation, it mitigates the bias of amplifying popular opinions and underrepresenting minority feedback[comet.com](https://www.comet.com/site/blog/llm-as-a-judge/#:~:text=Fairness%20refers%20to%20the%20LLM%E2%80%99s,LLM%20frequently%20scores%20certain%20writing).

## Evaluation Metric Design

Drawing from the literature on summarization and LLM fairness, we define six metrics to assess Airbnb review summaries:

1. **Coverage** – Measures the percentage of focus factors mentioned in the summary relative to those present in the reviews. Coverage operationalizes the “perspective coverage” attribute[aclanthology.org](https://aclanthology.org/2025.findings-acl.1268.pdf#:~:text=structing%20a%20test%20set%20to,from%20articles%20to%20create%20controlled) and ensures that the summary addresses all user‑specified aspects.
    
2. **Accuracy** – Assesses whether the summary reflects the sentiment and frequency of comments in the reviews without distortion. This metric penalizes exaggeration or understatement of how often issues are mentioned and whether sentiments are positive or negative.
    
3. **Faithfulness** – Evaluates whether all statements in the summary are directly supported by the reviews. Any hallucinated or unsupported claims lower the score. This aligns with the concept of “perspective faithfulness”[aclanthology.org](https://aclanthology.org/2025.findings-acl.1268.pdf#:~:text=structing%20a%20test%20set%20to,from%20articles%20to%20create%20controlled) and helps prevent the summary from incorporating external knowledge.
    
4. **Balance** – Judges whether contradictory opinions are represented proportionally. This metric addresses fairness concerns by discouraging the summarizer from skewing toward majority sentiment while ignoring minority views[comet.com](https://www.comet.com/site/blog/llm-as-a-judge/#:~:text=Fairness%20refers%20to%20the%20LLM%E2%80%99s,LLM%20frequently%20scores%20certain%20writing).
    
5. **Clarity** – Rates the readability, flow, and conciseness of the summary. Because LLM judges may favor verbosity[confident-ai.com](https://www.confident-ai.com/blog/why-llm-as-a-judge-is-the-best-llm-evaluation-method#:~:text=,to%20rely%20fully%20on%20them), clarity encourages succinct writing without sacrificing coherence.
    
6. **Relevance** – Measures whether the summary includes only information relevant to the focus factors. Irrelevant details or tangents reduce this score.
    

A coarse 0–5 integer scale is used for each metric because finer scales yield unreliable, arbitrary scores[confident-ai.com](https://www.confident-ai.com/blog/why-llm-as-a-judge-is-the-best-llm-evaluation-method#:~:text=%2A%20Not,reliable%20and%20more%20prone%20to). To compute an overall rating, each metric is weighted based on its importance, summing to 1.0. Higher weights on accuracy and coverage reflect their central role in summarization quality. The recommended weightings are presented below:

|Metric|Description|Scale|Weight|
|---|---|---|---|
|**Coverage**|Percentage of focus factors in the summary that are discussed and clearly indicated; includes explicit acknowledgment of absent factors|0–5|0.20|
|**Accuracy**|Correctness of sentiment and frequency portrayal relative to the reviews|0–5|0.25|
|**Faithfulness**|Degree to which all statements are traceable to review content without hallucination|0–5|0.20|
|**Balance**|Proportional representation of contradictory viewpoints|0–5|0.15|
|**Clarity**|Readability, flow, and conciseness of the summary|0–5|0.10|
|**Relevance**|Focus on information related only to the specified focus factors|0–5|0.10|

These metrics emphasize fairness, factuality, and user relevance. Coverage and faithfulness draw directly from recent research on summarization evaluation[aclanthology.org](https://aclanthology.org/2025.findings-acl.1268.pdf#:~:text=structing%20a%20test%20set%20to,from%20articles%20to%20create%20controlled), while balance addresses subtle biases in representation[comet.com](https://www.comet.com/site/blog/llm-as-a-judge/#:~:text=Fairness%20refers%20to%20the%20LLM%E2%80%99s,LLM%20frequently%20scores%20certain%20writing). Limiting the scale to 0–5 mitigates random variation in scoring[confident-ai.com](https://www.confident-ai.com/blog/why-llm-as-a-judge-is-the-best-llm-evaluation-method#:~:text=%2A%20Not,reliable%20and%20more%20prone%20to).

## Judge Prompt Design

The judge prompt instructs the LLM‑as‑a‑Judge to score a candidate summary against the original reviews and the focus factors using the metrics above. It must require:

- Verification that all expected input fields (reviews, focus factors, summary) are present.
    
- Individual scores (0–5 integer) for each metric with a one‑sentence justification.
    
- A weighted overall score computed as a float rounded to two decimal places using the given weights.
    
- Rounding down when a score falls between two integers to maintain consistency[confident-ai.com](https://www.confident-ai.com/blog/why-llm-as-a-judge-is-the-best-llm-evaluation-method#:~:text=%2A%20Not,reliable%20and%20more%20prone%20to).
    
- Concise English justifications that may cite representative review excerpts for clarity.
    
- JSON output with fields ordered as Coverage, Accuracy, Faithfulness, Balance, Clarity, Relevance, and Overall.
    

Here is the **Judge Prompt**:
You are an evaluator LLM tasked with scoring Airbnb property summaries.
You will be given:
1. The original set of reviews.
2. A list of focus factors defined by the user.
3. A candidate summary generated by another LLM.

First, verify that all inputs are present.  Then evaluate the summary using the following metrics: Coverage, Accuracy, Faithfulness, Balance, Clarity, and Relevance.  For each metric:
• Assign an integer score from 0 to 5 (no decimals).  Use 0 if the metric cannot be assessed or if the summary fails completely on that dimension.
• Provide a one‑sentence justification citing representative review evidence.  Keep explanations concise and in English.

If a score falls between two integers, round down to the lower value.  Do not reward verbosity; focus on factual correctness and proportional representation.  If the summary does not address any focus factors, assign 0 for Coverage and explain why.

After scoring each metric, compute the weighted overall score using the following weights: Coverage 0.20, Accuracy 0.25, Faithfulness 0.20, Balance 0.15, Clarity 0.10, Relevance 0.10.  Calculate the weighted average of your integer scores and round the result to two decimal places (0–5 scale).  Provide a brief justification for the overall score that explains how the scores aggregate.

Return your evaluation in the exact JSON format below, maintaining the field order:
{
  "Coverage": {"score": <integer 0–5>, "justification": "..."},
  "Accuracy": {"score": <integer 0–5>, "justification": "..."},
  "Faithfulness": {"score": <integer 0–5>, "justification": "..."},
  "Balance": {"score": <integer 0–5>, "justification": "..."},
  "Clarity": {"score": <integer 0–5>, "justification": "..."},
  "Relevance": {"score": <integer 0–5>, "justification": "..."},
  "Overall": {"score": <float 0–5>, "justification": "..."}
}
This judge prompt encourages consistent scoring by specifying a coarse scale, justifications, and a deterministic weighted calculation. It emphasises fairness by instructing the judge to avoid rewarding verbosity[confident-ai.com](https://www.confident-ai.com/blog/why-llm-as-a-judge-is-the-best-llm-evaluation-method#:~:text=,to%20rely%20fully%20on%20them) and to represent contradictory viewpoints proportionally[comet.com](https://www.comet.com/site/blog/llm-as-a-judge/#:~:text=Fairness%20refers%20to%20the%20LLM%E2%80%99s,LLM%20frequently%20scores%20certain%20writing). Requiring justifications improves transparency[comet.com](https://www.comet.com/site/blog/llm-as-a-judge/#:~:text=Explainability%20Metrics).

## Validation and Fairness Considerations

The proposed prompts and metrics directly address the challenges highlighted in recent research. By requiring explicit mention of unaddressed focus factors and proportional representation of contradictory sentiments, the **Summary Prompt**discourages selective reporting and mitigates bias toward majority opinions[comet.com](https://www.comet.com/site/blog/llm-as-a-judge/#:~:text=Fairness%20refers%20to%20the%20LLM%E2%80%99s,LLM%20frequently%20scores%20certain%20writing). Limiting the summary length reduces the influence of verbosity bias[confident-ai.com](https://www.confident-ai.com/blog/why-llm-as-a-judge-is-the-best-llm-evaluation-method#:~:text=,to%20rely%20fully%20on%20them). The **Evaluation Metrics** reflect core summary attributes (coverage and faithfulness[aclanthology.org](https://aclanthology.org/2025.findings-acl.1268.pdf#:~:text=structing%20a%20test%20set%20to,from%20articles%20to%20create%20controlled)) and fairness concerns (balance and relevance). Using a 0–5 scale prevents unreliable fine‑grained scoring[confident-ai.com](https://www.confident-ai.com/blog/why-llm-as-a-judge-is-the-best-llm-evaluation-method#:~:text=%2A%20Not,reliable%20and%20more%20prone%20to), and weighting emphasises the most critical aspects of summarization.

The **Judge Prompt** enforces a consistent scoring procedure, requiring justifications and a weighted overall score. It encourages transparency and supports human auditors in reviewing LLM judgments. By instructing evaluators to cite review evidence and avoid rewarding verbosity, it reduces susceptibility to length bias and hallucinations. Combining this automated evaluation with periodic human reviews, as recommended by industry frameworks[deepchecks.com](https://www.deepchecks.com/llm-evaluation-framework-steps-components/#:~:text=Key%20Takeaways), will further enhance fairness and reliability.

