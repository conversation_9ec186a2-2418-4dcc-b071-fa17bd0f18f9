```json
{
  "criteria": [
    {
      "name": "Required Structure & Formatting",
      "description": "Checks that the response uses the exact section headers and order specified, begins with a 3–7 bullet high-level checklist, and follows Markdown conventions (e.g., blockquotes for warnings, fenced code blocks for tables/markup).",
      "weight": 0.15,
      "scoring_guidelines": [
        { "score": 4, "description": "Begins with a 3–7 bullet conceptual checklist. All required sections appear exactly once, in order, with exact headers: '### Widget Name & Purpose', '### User Inputs', '### Outputs & Visualizations', '### Supported Analytical Techniques', '### Edge Cases & Error Handling', '### Performance & Usability Notes'. Uses Markdown blockquotes for warnings/errors and fenced code blocks where markup/tables are shown. No extra or missing required sections." },
        { "score": 3, "description": "Checklist present (3–7 bullets). All required sections present and in order, but minor header typos OR one formatting requirement (blockquote or fenced code block usage) is missing." },
        { "score": 2, "description": "Checklist present OR all required sections present, but not both; OR sections out of order; OR multiple formatting requirements unmet." },
        { "score": 1, "description": "Missing checklist and one or more required sections; headers substantially deviating from required text; formatting conventions largely ignored." },
        { "score": 0, "description": "Does not follow the required structure or formatting at all." }
      ]
    },
    {
      "name": "Purpose & Target Users Clarity",
      "description": "Quality of the restatement of the widget’s goal and explicit identification of target users per the prompt.",
      "weight": 0.07,
      "scoring_guidelines": [
        { "score": 4, "description": "Concise restatement of the widget name and main analytical goal; explicitly names target users (e.g., 'data analysts and data scientists'); aligns purpose with exploration of correlations and significance as applicable." },
        { "score": 3, "description": "Clear purpose and target users stated, but with minor omissions or generic phrasing." },
        { "score": 2, "description": "Purpose or target users present but vague or partially incorrect." },
        { "score": 1, "description": "Either purpose or target users missing; unclear or misleading." },
        { "score": 0, "description": "Both purpose and target users missing or incorrect." }
      ]
    },
    {
      "name": "User Inputs Specification Quality",
      "description": "Completeness and specificity of inputs, including input name, data type, default value, validation rules, and constraints; use of 'Not specified.' for any missing info.",
      "weight": 0.18,
      "scoring_guidelines": [
        { "score": 4, "description": "≥90% of listed inputs include all five fields (name, data type, default, validation, constraints). Validation rules and constraints are concrete (e.g., 'numeric only', 'alpha in [0,1]', 'min sample size ≥ 30'). Any unknowns explicitly marked 'Not specified.' without guessing." },
        { "score": 3, "description": "≥75% of inputs include all five fields; the rest have only one field missing OR generic validation wording." },
        { "score": 2, "description": "≥50% of inputs include all five fields; multiple inputs lack validation/constraints or defaults; inconsistent use of 'Not specified.'." },
        { "score": 1, "description": "<50% of inputs include all five fields; frequent omissions; little to no validation/constraints." },
        { "score": 0, "description": "Inputs largely absent or presented without required fields." }
      ]
    },
    {
      "name": "Outputs & Visualization Design Quality",
      "description": "Coverage and correctness of outputs (heatmap, tables, stats), interaction design, and visualization best practices for correlation analysis (e.g., diverging scale around 0, labels, clustering/masking options).",
      "weight": 0.17,
      "scoring_guidelines": [
        { "score": 4, "description": "Spec lists all key outputs: correlation heatmap, coefficient and p-value display, optional table, alerts. Interactions (tooltips with coefficient, p-value, n; hover, selection, export) are specified. Visualization best practices explicitly included (diverging color scale centered at 0, legend, optional upper/lower triangle mask, variable ordering/clustering, handling of diagonal=1, annotation density controls)." },
        { "score": 3, "description": "Most outputs and interactions present with minor omissions (e.g., missing legend or clustering option) but overall aligned with best practices." },
        { "score": 2, "description": "Some outputs listed, but limited interaction detail or multiple best-practice gaps (e.g., no diverging scale or unclear annotations)." },
        { "score": 1, "description": "Outputs/visualizations described superficially; interactions minimal; several best-practice violations." },
        { "score": 0, "description": "Outputs and visualization design largely missing or unsuitable for correlation exploration." }
      ]
    },
    {
      "name": "Analytical Techniques Coverage & Accuracy",
      "description": "Correct listing and description of supported techniques (e.g., Pearson, Spearman, Kendall) and any input requirements/assumptions; notes on statistical significance and multiple testing as relevant to the example.",
      "weight": 0.10,
      "scoring_guidelines": [
        { "score": 4, "description": "Techniques correctly described with when-to-use guidance (Pearson: linear, continuous; Spearman/Kendall: monotonic/ranks, ties handled). Notes numeric-only variables, handling of ties/ordinal data, and significance (p-values, CI) with optional multiple-testing control (e.g., FDR). Any technique-specific parameterization (e.g., pairwise vs listwise deletion) stated." },
        { "score": 3, "description": "Techniques listed correctly with brief applicability notes; minor omissions (e.g., no mention of multiple testing or missing-data method)." },
        { "score": 2, "description": "Techniques listed but applicability/assumptions vague or partially incorrect." },
        { "score": 1, "description": "Techniques incomplete or with substantial inaccuracies." },
        { "score": 0, "description": "Techniques missing or incorrect." }
      ]
    },
    {
      "name": "Edge Cases & Error Handling",
      "description": "Enumerates likely data/UX edge cases and provides sample user-facing messages in Markdown blockquotes; defines feedback mechanisms (warnings, errors, guidance).",
      "weight": 0.12,
      "scoring_guidelines": [
        { "score": 4, "description": "At least six relevant edge cases covered (e.g., non-numeric columns, zero-variance features, insufficient rows, missing values handling, incompatible technique, too many variables to render, perfect ±1 correlations). Each includes concrete guidance and a sample message using blockquote (e.g., '> **Warning:** ...'). Feedback mechanism (warning vs error) is specified." },
        { "score": 3, "description": "Four–five edge cases with sample messages; minor specificity gaps." },
        { "score": 2, "description": "Two–three edge cases; messages generic or missing formatting; feedback mechanisms unclear." },
        { "score": 1, "description": "One edge case or generic statements without actionable messaging." },
        { "score": 0, "description": "No edge cases or error handling guidance." }
      ]
    },
    {
      "name": "Performance & Usability Considerations",
      "description": "Concrete recommendations for large datasets and responsive UX (e.g., sampling/aggregation, async computation, caching, virtualization, progressive rendering, export).",
      "weight": 0.08,
      "scoring_guidelines": [
        { "score": 4, "description": "Multiple concrete strategies tied to this widget type: variable pre-filtering, max matrix size with downsampling, clustering precompute, async/paginated stats, UI virtualization, debounce on parameter changes, export options (CSV/PNG/SVG), accessibility notes (colorblind-safe palettes) stated as recommendations." },
        { "score": 3, "description": "Several relevant strategies provided but with minor gaps or generic phrasing." },
        { "score": 2, "description": "One–two general strategies; limited specificity to correlation heatmaps." },
        { "score": 1, "description": "Vague performance/usability statements with little applicability." },
        { "score": 0, "description": "No performance or usability guidance." }
      ]
    },
    {
      "name": "Handling of Ambiguity & Gaps",
      "description": "Explicitly marks missing information as 'Not specified.' and avoids unfounded assumptions; flags impacts on the spec where appropriate.",
      "weight": 0.05,
      "scoring_guidelines": [
        { "score": 4, "description": "All unknowns are marked 'Not specified.' in the relevant fields; the spec flags how these gaps affect defaults, validation, or feasibility without inventing facts." },
        { "score": 3, "description": "Unknowns generally marked, with rare omissions; minimal speculative content." },
        { "score": 2, "description": "Some unknowns marked, but several speculative assumptions made without flagging." },
        { "score": 1, "description": "Frequent unmarked assumptions; 'Not specified.' seldom used." },
        { "score": 0, "description": "Ignores ambiguity; fills gaps with invented details throughout." }
      ]
    },
    {
      "name": "Precision, Readability, and Framework-Agnosticism",
      "description": "Clarity and concision of writing; avoids framework/library-specific implementation details; uses consistent terminology suited for data analysts/scientists.",
      "weight": 0.04,
      "scoring_guidelines": [
        { "score": 4, "description": "Concise, precise language; consistent terminology (e.g., 'pairwise deletion', 'false discovery rate'); no framework/library names or code; section content is readable and unambiguous." },
        { "score": 3, "description": "Mostly clear with minor verbosity or occasional tool-specific terms that do not constrain implementation." },
        { "score": 2, "description": "Noticeable verbosity or occasional framework-specific directions; some inconsistent terms." },
        { "score": 1, "description": "Hard to read; multiple framework-specific instructions that violate 'framework-agnostic' guidance." },
        { "score": 0, "description": "Unclear and implementation-bound writing throughout." }
      ]
    },
    {
      "name": "Actionability & Cross-Section Consistency",
      "description": "Internal consistency across inputs, techniques, and outputs; traceability from parameters to behaviors; constraints align with visualizations and analytics.",
      "weight": 0.04,
      "scoring_guidelines": [
        { "score": 4, "description": "Inputs map clearly to outputs/techniques (e.g., a 'significance_level' input governs p-value thresholding/annotations; 'method' selects Pearson/Spearman/Kendall). Constraints and validation are reflected in edge cases and visual behavior. No contradictions found." },
        { "score": 3, "description": "Generally consistent with minor mismatches (e.g., input named but not referenced in outputs)." },
        { "score": 2, "description": "Multiple inconsistencies (e.g., unsupported technique referenced; constraints conflict with visual design)." },
        { "score": 1, "description": "Largely inconsistent; difficult to implement due to contradictions." },
        { "score": 0, "description": "Internally incoherent; non-actionable." }
      ]
    }
  ],
  
}
```
## Notes
This rubric is tailored to the provided system prompt and the example input for a correlation matrix widget supporting Pearson, Spearman, and Kendall with significance. Automated checks can search for exact section headers, presence of a 3–7 bullet checklist at the top, required input subfields, blockquoted warnings, and keywords (e.g., 'diverging', 'p-value', 'FDR'). Some aspects are underspecified by the prompt (e.g., default significance threshold, dataset schema, maximum variable count); evaluators should accept 'Not specified.' where appropriate and avoid penalizing lack of invented details. reasoning_effort = medium.