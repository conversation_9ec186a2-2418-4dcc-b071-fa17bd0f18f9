Developer: Begin with a concise checklist (3-7 bullets) of what you will do; keep items conceptual, not implementation-level. Generate an evaluation rubric for a large language model (LLM) specifically tailored to the provided prompt, ensuring that the rubric reflects the unique characteristics and goals of the prompt to facilitate comprehensive analysis of outputs from one or more language models. Clearly define evaluation criteria, weight their importance such that total weight is 1, and provide explicit, actionable scoring guidelines for each criterion. If the prompt is ambiguous or lacking information, include a note explaining potential impacts on evaluation quality or consistency. The rubric should be suitable for both manual and automated evaluation. Set reasoning_effort = medium to ensure criteria are relevant, weighted appropriately, and scoring guidance is specific. After generating the rubric, quickly verify that all criteria are present, weights sum to 1, and scoring guidelines are complete and actionable; if not, revise before returning.

## Output Format
Produce the rubric as a JSON object using the following schema:
{
  "criteria": [
    {
      "name": "<string: Name of the criterion>",
      "description": "<string: Description of what this criterion evaluates>",
      "weight": <number: Relative importance from 0 to 1>,
      "scoring_guidelines": [
        {
          "score": <integer: Possible score>,
          "description": "<string: Requirements for a response to earn this score>"
        }
      ]
    }
    // ...additional criteria
  ],
  "notes": "<string: Additional context or caveats for applying the rubric>"
}

- Ensure the total weight of all criteria equals 1.
- Provide clear, actionable scoring guidelines for every criterion.
- If the prompt is ambiguous or lacking information, include a note explaining how this may impact evaluation quality or consistency.
- The rubric should be suitable for both manual and automated evaluation.

Prompt: 