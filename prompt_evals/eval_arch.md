# Prompt Evaluation Benchmark — Architecture (with Promptfoo integration)

## Context
Local, Dockerized Python app for a 2–3 dev/DS team, low execution volume, CLI/SDK-first, minimal REST, heavy focus on prompt variant × LLM evaluations, double‑blind LLM‑as‑judge, validators, weighted aggregation, reproducible logs.

---

### ✅ Checklist (what I’ll do)
- Evaluate frameworks/libraries (Promptfoo + peers) and recommend an approach.
- Propose an end‑to‑end architecture that integrates Promptfoo within a modular Python system.
- Detail component boundaries & lifecycle (orchestrator, runners, validators, judges, stats, storage, reporting).
- Plan observability & data strategy (logging, configuration, persistence, telemetry).
- Deliver developer assets: diagrams (Mermaid), component responsibilities, and Python skeleton code (incl. a Promptfoo adapter).
- List open questions/inputs and a concise README‑style spec.

---

## 1) Framework Evaluation (incl. Promptfoo)

| Name | Pros | Cons | License | Rationale |
|------|------|------|---------|-----------|
| Promptfoo | Purpose‑built CLI & config‑driven LLM evals; supports providers, prompts, test datasets; can output JSON/CSV/HTML results; supports --repeat (replicates), --grader (LLM judge), and JSON outputs via --output; includes model‑graded assertions (e.g., llm-rubric) for LLM‑as‑judge; tracks latency, token usage, cost when available; ships a container image for easy Docker use; broad model provider support. | Node/TS stack (adds a small runtime to your Python app); its default aggregation semantics may differ from your weighted multi‑metric + CI design (we’ll parse raw results and do aggregation in Python). | MIT. | Recommended as the execution & grading harness: we generate Promptfoo config, run promptfoo eval, then import JSON and aggregate per your rules. (Keeps your Python core minimal and reproducible.) |
| DeepEval (Confident AI) | Python‑native testing framework w/ many built‑in metrics (G‑Eval, hallucination, RAG metrics), pytest‑like usage; good for validators & metric plugins. | Focused on metrics/testing rather than grid execution & report generation; no built‑in double‑blind packetization; requires coding tests. | Apache‑2.0. | Great as validator plugins inside our pipeline (e.g., hallucination, RAG faithfulness). |
| TruLens | Python library for evaluation & tracing with feedback functions and observability; MIT‑licensed. | More observability/tracing‑oriented than strict reproducible benchmarking; not a turn‑key judge/replicate grid. | MIT. | Optional telemetry/tracing for advanced debugging. |
| Ragas | Strong RAG metrics and tutorials; Python; Apache‑2.0. | Not a general prompt grid runner; best for RAG scoring, not full LLM‑as‑judge rubrics. | Apache‑2.0. | Use as task‑specific validators (RAG pipelines). |
| OpenAI Evals | Canonical eval patterns; community evals; MIT. | Skews to OpenAI ecosystem; less flexible for multi‑provider double‑blind & your DB schema. | MIT. | Source of ideas; not primary runner for your multi‑provider goals. |
| Custom Python Orchestrator | Full control; minimal deps; easy to align with your schemas and tie‑break/CIs. | Must implement job graph, retries, seed control, etc. | Your choice (MIT/Apache). | We will build this, but delegate execution & grading to Promptfoo to reduce scope/ops burden. |

### Recommendation:
Use a Hybrid: Custom Python orchestrator (planning, blinding, persistence, aggregation, reports) + Promptfoo for generation & LLM‑as‑judge scoring (via config + CLI). Add DeepEval/Ragas as optional validators for specific tasks (JSON schema, RAG). This matches local Docker use, low volume, and your custom stats needs, while leveraging Promptfoo’s robust eval/outputs and judge tooling. CLI features we’ll rely on include: --output (JSON), --repeat (replicates), --grader or assertion‑level provider override, and provider coverage.

---

## 2) End‑to‑End Architecture (Promptfoo‑enabled)

### 2.1 Component Responsibilities
- CLI/SDK (bench run/report/export): User entrypoints.
- Orchestrator (Python): Parse RunManifest, expand (task×input×prompt×model×k), seed PRNG; build blinded JudgePackets; generate Promptfoo config; call Promptfoo; import JSON results; compute aggregates (weighted means, CIs), tie‑break; generate Markdown/CSV/Parquet.
- Promptfoo Runner (Adapter): Render config YAML from manifest; include prompts, providers, tests, assertions (validators + rubric assertions); invoke promptfoo eval --repeat K --output results.json in a working directory; capture artifacts. (Use GHCR image or local install.)
- Validators:
  - Deterministic in Promptfoo config (e.g., regex, is‑json, classifier) and/or Python validators (DeepEval/Ragas) for extra checks.
- Judging (LLM‑as‑Judge): Promptfoo llm-rubric assertion(s), with rubricPrompt set to enforce double‑blind (no model/prompt IDs), and provider override per judge model (multi‑judge).
- Aggregator/Stats (Python): Roll up per (task,prompt,model) across replicates & judges; compute SD/SE/95% CI; apply weights; tie policy (cost or pass‑rate).
- Storage: SQL DB (SQLite/Postgres) for entities; object store (local fs) for configs/results/reports; cost/latency roll‑ups.
- Minimal REST (optional): POST /runs, GET /runs/{id}/status, GET /runs/{id}/report.

### 2.2 Sequence (Happy Path)

```mermaid
sequenceDiagram
autonumber
participant CLI as CLI/SDK
participant Orc as Orchestrator (Python)
participant PF as Promptfoo Runner (CLI)
participant LLM as Providers (OpenAI/Anthropic/...)
participant DB as SQL DB
participant FS as Object Store

CLI->>Orc: bench run --manifest run.yaml
Orc->>DB: Persist Run/Manifest (hash, seeds)
Orc->>Orc: Expand matrix (task×input×prompt×model×k)
Orc->>Orc: shuffle via seed
Orc->>PF: Generate promptfooconfig.yaml (+ blinded rubricPrompt)
Orc->>PF: call `promptfoo eval --repeat K --output results.json`
PF->>LLM: Call providers per config (prompts × tests)
PF->>LLM: run assertions (validators + llm-rubric)
LLM-->>PF: Outputs + usage/cost/latency
PF-->>FS: Write results.json, logs, cache
PF-->>Orc: Return exit code
PF-->>Orc: point to results.json
Orc->>FS: Ingest results.json
Orc->>FS: archive config, logs
Orc->>DB: Upsert Generations, ValidatorResults, JudgeScores
Orc->>Orc: Aggregate (weighted means, SE/CI, tie-break)
Orc->>FS: Write CSV/Parquet + Markdown reports
Orc->>DB: Update run status summary
Orc-->>CLI: Summary + report paths
```

### 2.3 Module Interaction (with Promptfoo, Validators, Judges)

```mermaid
flowchart LR
subgraph App["Local Docker App"]
CLI["CLI / Python SDK"]:::iface --> ORCH["Orchestrator"]:::comp
ORCH -->|render| CFG["promptfooconfig.yaml"]:::artifact
ORCH --> PF["Promptfoo Adapter"]:::comp
PF -->|exec| PFCLI["promptfoo eval"]:::comp
PFCLI -->|providers| PROV["OpenAI / Anthropic / ..."]:::ext
PFCLI --> RES["results.json"]:::artifact
ORCH -->|parse| RES
ORCH --> VALS["Python Validators (DeepEval/Ragas)"]:::comp
ORCH --> AGG["Aggregator/Stats"]:::comp
AGG --> REPT["Reporter (MD/CSV/Parquet)"]:::comp
end
ORCH .-> DB[(SQL DB)]:::store
REPT .-> FS[(Artifacts Store)]:::store
CFG -. "archived" .-> FS
RES -. "archived" .-> FS

classDef comp fill:#e8f0fe,stroke:#5a78c3
classDef iface fill:#fff,stroke:#444,stroke-dasharray:3 3
classDef store fill:#fff4ce,stroke:#deb974
classDef artifact fill:#e6ffed,stroke:#34a853
classDef ext fill:#fce8e6,stroke:#ea4335
```

---

## 3) Promptfoo in This System — How We Use It
- Config generation: For each run, we render a single Promptfoo config that includes:
  - prompts: your PromptVariants.
  - providers: your TargetModels mapped to Promptfoo providers (e.g., openai:gpt-4.1, anthropic:messages:claude-sonnet-4-20250514).
  - tests: built from Task.inputs (CSV/JSON/JSONL/inline).
  - defaultTest.assert: Deterministic checks (e.g., is-json, regex) and model‑graded rubric assertions (llm-rubric) with metric names and weights (we also compute weighted means in Python to match your 25/20/20/15/10/10 policy).
  - Double‑blind: We supply a custom rubricPrompt that presents only the task spec, acceptance criteria, validator summary, and the anonymous output—no prompt/model identifiers. Provider override per assertion lets us run multiple judge models.
  - Replicates: Use --repeat K (and, where supported, set seed per provider config) to collect K samples per (task, prompt, model). We store each replicate as a distinct Generation.
  - Outputs & cost/latency: Promptfoo exports JSON with outputs; when available it includes latency, token usage, and cost estimates—we ingest and store these fields.
  - Pairwise/Select‑Best (optional): For head‑to‑head prompt comparisons on the same input we can add select-best assertions.
  - Containerized: We can run Promptfoo via GHCR image inside Docker Compose for reproducibility; or install via npm/brew.

---

## 4) Observability & Data Strategy
- Storage:
  - DB (SQLite to start; Postgres optional later): runs, tasks, inputs, prompts, models, generations, validator_results, judge_scores, aggregates.
  - Artifacts: /runs/{run_id}/configs/promptfooconfig.yaml, /runs/{run_id}/results/results.json, /runs/{run_id}/reports/*.md|.csv|.parquet.
  - Logging: Structured JSON logs (run_id, job keys, timings, retries). Archive config + results for replay/repro.
  - Reproducibility: Content‑hash manifest & config, log provider model/version, decoding params, seeds; record Promptfoo & adapter versions.
  - Security: Secrets via env/Docker secrets (OPENAI_API_KEY, etc). No PII in inputs by design.
  - Telemetry (optional): Add TruLens for tracing & feedback functions when debugging complex cases.

---

## 5) Architecture Diagrams (Mermaid)

### 5.1 Run Lifecycle — with Promptfoo (sequence)

```mermaid
sequenceDiagram
participant U as User (CLI)
participant O as Orchestrator
participant A as Promptfoo Adapter
participant P as Promptfoo CLI
participant S as Providers
participant D as DB
participant F as FS (Artifacts)

U->>O: bench run --manifest run.yaml
O->>D: Create Run
O->>D: store manifest/hash/seeds
O->>A: Build config (prompts/providers/tests/assertions)
A->>F: Write promptfooconfig.yaml
O->>P: promptfoo eval --repeat K --output results.json --no-table --no-progress-bar
P->>S: Execute prompts × tests × providers (+ assertions)
S-->>P: Outputs (+ token/latency/cost)
P-->>F: results.json (+ logs/cache)
O->>F: Ingest results.json, link artifacts
O->>D: Persist Generations, ValidatorResults, JudgeScores
O->>O: Aggregate (weighted mean, CI, ties)
O->>F: Emit CSV/Parquet + Markdown report
O-->>U: Summary + report path
```

### 5.2 Data Flow — import/aggregate/report

```mermaid
flowchart TD
R["results.json"] -->|parse| G["Generations"] --> A["Aggregator"]
R --> V["ValidatorResults"] --> A
R --> J["JudgeScores"] --> A
A --> AGG["AggregateScore table"]
AGG --> MD["Markdown tables"]
AGG --> CSV["CSV/Parquet"]
G --> COST["Cost/latency rollup"]
COST --> MD
```

---

## 6) Skeleton Code (Python, typed)

### Goal
Minimal working flow demonstrating Promptfoo integration. The actual DB/SDK wiring and error handling abbreviated for brevity.

### 6.1 Data Models (aligning to your minimal schemas)

```python
# models.py
from __future__ import annotations
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class RunManifest:
    run_id: str
    created_at: str
    task_suite: List[str]
    prompt_variants: List[str]
    targets: List[str]
    replicates: int
    decoding_defaults: Dict[str, float]
    randomization_seed: int
    judge_pool: List[str]
    evaluation_plan: Dict[str, str]  # {"rubric_version": "v1", "weights_profile": "default-v1"}

@dataclass
class Task:
    task_id: str
    name: str
    spec: str
    acceptance_criteria: List[str]
    ground_truth: Optional[Dict]  # optional
    validators: List[str]
    inputs: List[Dict]            # [{"input_id": "...", "payload": {"text": "...", ...}}, ...]

@dataclass
class PromptVariant:
    prompt_id: str
    strategy_label: str
    prompt_text: str
    placeholders: List[str]
    anonymized_hash: str

@dataclass
class TargetModel:
    model_id: str
    provider: str  # e.g., "openai" | "anthropic"
    name: str
    version: str
    decoding_params: Dict[str, float]
    cost_estimator: Dict[str, float]  # per 1k

@dataclass
class Generation:
    gen_id: str
    run_id: str
    task_id: str
    input_id: str
    prompt_id: str
    model_id: str
    replicate_ix: int
    seed: int
    output: Dict[str, str]
    token_usage: Dict[str, int]
    latency_ms: int
    status: str
    error: Optional[str]

@dataclass
class ValidatorResult:
    val_id: str
    gen_id: str
    validator_id: str
    name: str
    passed: bool
    details: Dict

@dataclass
class JudgeScore:
    score_id: str
    packet_id: str
    judge_model_id: str
    metrics: Dict[str, int]
    notes: str
    mean_score: float  # 0-100

@dataclass
class AggregateScore:
    agg_id: str
    run_id: str
    prompt_id: str
    model_id: str
    task_id: str
    replicate_mean: float
    replicate_std: float
    replicate_se: float
    ci_lower: float
    ci_upper: float
```

### 6.2 Promptfoo Config Rendering (blinded LLM‑as‑judge)

```python
# promptfoo_adapter.py
from __future__ import annotations
import json, subprocess, uuid, tempfile
from pathlib import Path
from typing import Any, Dict, Iterable, List, Tuple
import yaml

from models import RunManifest, Task, PromptVariant, TargetModel

RUBRIC_METRICS = [
    ("accuracy", 25),
    ("adherence", 20),
    ("completeness", 20),
    ("clarity", 15),
    ("conciseness", 10),
    ("creativity", 10),
]

def _judge_assertions(judge_pool: List[TargetModel], criteria: List[str]) -> List[Dict[str, Any]]:
    """
    Build promptfoo llm-rubric assertions, one per metric per judge model.
    Each assertion has a metric name and weight; provider override routes to judge model.
    """
    rubric_text = (
        "Evaluate the OUTPUT against the TASK and ACCEPTANCE CRITERIA.\n"
        "Return JSON {reason, pass, score} with score in [0.0,1.0].\n"
        "Do not infer the model or prompt identity."
    )
    metric_asserts: List[Dict[str, Any]] = []
    for m, weight in RUBRIC_METRICS:
        for judge in judge_pool:
            metric_asserts.append({
                "description": f"{m} judged by {judge.name}",
                "metric": m,
                "weight": weight,  # also recomputed in Python
                "type": "llm-rubric",  # promptfoo model-graded assertion
                "value": f"Criterion: {m}. Also ensure: " + "; ".join(criteria),
                # Enforce double-blind rubricPrompt that excludes model/prompt IDs
                "provider": _pf_provider_id(judge),
                "options": {
                    "rubricPrompt": [
                        {"role": "system",
                         "content": "You are a strict grader. Respond ONLY with JSON {reason, pass, score}."},
                        {"role": "user",
                         "content": "TASK: {{vars.task_spec}}\nACCEPTANCE: {{vars.acceptance_criteria}}\nOUTPUT: {{output}}"}
                    ]
                }
            })
    return metric_asserts

def _deterministic_assertions(task: Task) -> List[Dict[str, Any]]:
    """Optional deterministic validators via promptfoo."""
    out: List[Dict[str, Any]] = []
    if "is_json" in task.validators:
        out.append({"type": "is-json"})  # promptfoo 'is-json' ⇒ valid JSON (+ optional schema)
    if "no_toxicity" in task.validators:
        out.append({
            "type": "not-classifier",
            "provider": "huggingface:token-classification:bigcode/starpii",  # example classifier
            "threshold": 0.75,
            "description": "Output must not contain PII"
        })
    # Additional regex/length checks can be added similarly
    return out

def _pf_provider_id(model: TargetModel) -> str:
    """Map our TargetModel to a promptfoo provider string."""
    if model.provider == "openai":
        return f"openai:{model.name}"
    if model.provider == "anthropic":
        return f"anthropic:messages:{model.name}"
    return model.name  # fallback (custom HTTP provider etc.)

def build_promptfoo_config(
    manifest: RunManifest,
    tasks: List[Task],
    prompts: List[PromptVariant],
    targets: List[TargetModel]
) -> Dict[str, Any]:
    """Render a single promptfoo config covering the whole run."""
    # Providers
    providers: List[Any] = []
    for t in targets:
        providers.append({
            "id": _pf_provider_id(t),
            "label": f"{t.provider}:{t.name}",
            "config": {**t.decoding_params, **({"seed": manifest.decoding_defaults.get("seed", 0)} if "seed" in manifest.decoding_defaults else {})}
        })

    # Prompts
    pf_prompts: List[str] = [p.prompt_text for p in prompts]

    # Tests (each task input becomes a test case with vars available to prompts/assertions)
    tests: List[Dict[str, Any]] = []
    for task in tasks:
        for inp in task.inputs:
            tests.append({
                "description": f"{task.name}::{inp['input_id']}",
                "vars": {
                    "task_spec": task.spec,
                    "acceptance_criteria": "; ".join(task.acceptance_criteria),
                    **inp["payload"],
                },
                "assert": _deterministic_assertions(task) + _judge_assertions(
                    judge_pool=[next(m for m in targets if m.model_id == j) for j in manifest.judge_pool],
                    criteria=task.acceptance_criteria
                )
            })

    # Default command line options (can be overridden by CLI)
    cfg: Dict[str, Any] = {
        "prompts": pf_prompts,
        "providers": providers,
        "tests": tests,
        "commandLineOptions": {
            "maxConcurrency": 5,   # local-friendly
            "share": False,
            "table": False
        }
    }
    return cfg

def run_promptfoo(config: Dict[str, Any], repeat: int, workdir: Path) -> Path:
    """Write config to disk and run promptfoo eval. Returns path to results.json."""
    workdir.mkdir(parents=True, exist_ok=True)
    cfg_path = workdir / "promptfooconfig.yaml"
    with cfg_path.open("w") as f:
        yaml.safe_dump(config, f, sort_keys=False)
    out_path = workdir / "results.json"
    cmd = ["promptfoo", "eval", "-c", str(cfg_path), "--repeat", str(repeat),
           "--no-table", "--no-progress-bar", "-o", str(out_path)]
    subprocess.run(cmd, cwd=str(workdir), check=True)
    return out_path
```

### 6.3 Results Import & Aggregation

```python
# import_and_aggregate.py
from __future__ import annotations
import json, math, uuid
from typing import Any, Dict, List, Tuple
from models import Generation, ValidatorResult, JudgeScore, AggregateScore

def import_promptfoo_results(run_id: str, path: str) -> Tuple[List[Generation], List[ValidatorResult], List[JudgeScore]]:
    """
    Parse promptfoo results.json to our schema.
    NOTE: Promptfoo's JSON schema may evolve; this parser should be resilient:
          - Each row typically corresponds to (test, prompt, provider, possibly replicate).
          - Include fields if available: latency_ms, token_usage, cost.
    """
    with open(path, "r") as f:
        raw = json.load(f)

    generations: List[Generation] = []
    validators: List[ValidatorResult] = []
    judge_scores: List[JudgeScore] = []

    # Illustrative traversal: adapt to actual structure (rows / results)
    for row in raw.get("results", []):
        # Extract our keys from promptfoo row
        prompt_text = row.get("prompt", "")
        provider_label = row.get("provider", "")
        test_desc = row.get("description", "")
        replicate_ix = int(row.get("repeatIndex", 0))
        output_text = row.get("output", "")
        lat = int(row.get("latencyMs", 0))
        usage = row.get("tokenUsage", {})
        status = "ok" if row.get("error") is None else "error"

        # Map to our IDs (you can carry mapping tables in orchestrator context)
        gen_id = str(uuid.uuid4())
        task_id, input_id = _split_test_desc(test_desc)
        prompt_id = _lookup_prompt_id(prompt_text)
        model_id = _lookup_model_id(provider_label)

        generations.append(Generation(
            gen_id=gen_id, run_id=run_id, task_id=task_id, input_id=input_id,
            prompt_id=prompt_id, model_id=model_id, replicate_ix=replicate_ix,
            seed=0, output={"text": output_text},
            token_usage={"prompt": usage.get("prompt", 0), "completion": usage.get("completion", 0), "total": usage.get("total", 0)},
            latency_ms=lat, status=status, error=row.get("error")
        ))

        # Deterministic validators (is-json/regex/classifier) appear as assertion results
        for a in row.get("assertions", []):
            if a.get("type", "").startswith("llm-"):
                continue  # judge metrics handled below
            validators.append(ValidatorResult(
                val_id=str(uuid.uuid4()),
                gen_id=gen_id,
                validator_id=a.get("type", "custom"),
                name=a.get("type", "custom"),
                passed=bool(a.get("pass", False)),
                details={"score": a.get("score"), "reason": a.get("reason")}
            ))

        # LLM-as-judge results: collect named metrics and scale 0..1 to 0..100
        metric_scores: Dict[str, int] = {}
        notes = []
        for a in row.get("assertions", []):
            if a.get("type") == "llm-rubric":
                metric_name = a.get("metric", "overall")
                scr = a.get("score")
                if isinstance(scr, (int, float)):
                    metric_scores[metric_name] = int(round(scr * 100))
                reason = a.get("reason")
                if reason:
                    notes.append(f"{metric_name}: {reason}")

        if metric_scores:
            # mean_score is simple unweighted mean here; Python aggregator will recompute with weights
            ms = sum(metric_scores.values()) / max(1, len(metric_scores))
            judge_scores.append(JudgeScore(
                score_id=str(uuid.uuid4()),
                packet_id=gen_id,  # 1:1 with generation in this import (packetization enforced in config)
                judge_model_id="multi",  # we can expand one per judge assertion if desired
                metrics=metric_scores,
                notes=" | ".join(notes)[:500],
                mean_score=float(ms)
            ))

    return generations, validators, judge_scores

def aggregate(run_id: str, judge_scores: List[JudgeScore], weights: Dict[str, float]) -> List[AggregateScore]:
    # Group by (task_id, prompt_id, model_id); here we assume we can reverse-map score_id→gen→keys
    # For brevity, suppose judge_scores include these keys in metrics (attach externally as needed)
    groups: Dict[Tuple[str, str, str], List[JudgeScore]] = {}
    for js in judge_scores:
        key = _group_key_from_packet(js.packet_id)  # implement via lookup table
        groups.setdefault(key, []).append(js)

    agg: List[AggregateScore] = []
    for (task_id, prompt_id, model_id), scores in groups.items():
        # Weighted mean per judge result, then aggregate across replicates (treat each judge assertion as a sample)
        sample_vals: List[float] = []
        for s in scores:
            wsum = sum(weights.get(m, 0) for m in s.metrics)
            if wsum == 0:
                continue
            val = sum((s.metrics[m] * weights.get(m, 0)) for m in s.metrics) / wsum
            sample_vals.append(val)

        if not sample_vals:
            continue

        mean = sum(sample_vals) / len(sample_vals)
        variance = sum((x - mean) ** 2 for x in sample_vals) / max(1, len(sample_vals))
        std = math.sqrt(variance)
        se = std / math.sqrt(len(sample_vals))
        ci_lo, ci_hi = mean - 1.96 * se, mean + 1.96 * se

        agg.append(AggregateScore(
            agg_id=str(uuid.uuid4()), run_id=run_id,
            prompt_id=prompt_id, model_id=model_id, task_id=task_id,
            replicate_mean=mean, replicate_std=std, replicate_se=se,
            ci_lower=ci_lo, ci_upper=ci_hi
        ))
    return agg
```

### 6.4 Orchestrator (Python) — minimal

```python
# orchestrator.py
from __future__ import annotations
import random, uuid
from pathlib import Path
from typing import List, Dict
from models import RunManifest, Task, PromptVariant, TargetModel
from promptfoo_adapter import build_promptfoo_config, run_promptfoo
from import_and_aggregate import import_promptfoo_results, aggregate

WEIGHTS_DEFAULT = {"accuracy":25, "adherence":20, "completeness":20, "clarity":15, "conciseness":10, "creativity":10}

class Orchestrator:
    def __init__(self, db, store_dir: Path):
        self.db = db
        self.store_dir = store_dir

    def execute(self, manifest: RunManifest, tasks: List[Task], prompts: List[PromptVariant], models: List[TargetModel]) -> str:
        run_dir = self.store_dir / manifest.run_id
        run_dir.mkdir(parents=True, exist_ok=True)

        # Persist manifest & hash
        self.db.create_run(manifest)

        # Shuffle job order (Promptfoo runs the full grid; shuffling can pre-order tests)
        random.Random(manifest.randomization_seed).shuffle(tasks)

        # Render and run Promptfoo
        cfg = build_promptfoo_config(manifest, tasks, prompts, models)
        results_path = run_promptfoo(cfg, repeat=manifest.replicates, workdir=run_dir)

        # Import results and aggregate
        gens, vals, judges = import_promptfoo_results(manifest.run_id, str(results_path))
        self.db.bulk_upsert_generations(gens)
        self.db.bulk_upsert_validator_results(vals)
        self.db.bulk_upsert_judge_scores(judges)

        aggs = aggregate(manifest.run_id, judges, weights=WEIGHTS_DEFAULT)
        self.db.bulk_upsert_aggregates(aggs)

        # Emit reports (CSV/MD/Parquet) — omitted for brevity
        # report_paths = reporter.write(aggs, run_dir)

        self.db.update_run_status(manifest.run_id, status="completed")
        return str(run_dir)
```

---

## 7) Example Promptfoo Config Snippet (generated)

```yaml
# promptfooconfig.yaml (excerpt)
prompts:
  - |
    You are an assistant. Task: {{task_spec}}
    Input: {{text}}
    Produce a concise answer satisfying all acceptance criteria.

providers:
  - id: openai:gpt-4.1
    label: openai:gpt-4.1
    config:
      temperature: 0.2
      top_p: 1.0
      seed: 13
  - id: anthropic:messages:claude-sonnet-4-20250514
    label: anthropic:claude-sonnet-4
    config:
      temperature: 0.2

tests:
  - description: summarization-short-news::{{input_id}}
    vars:
      task_spec: "{{task_spec}}"
      acceptance_criteria: "{{acceptance_criteria}}"
      text: "{{text}}"
    assert:
      # Deterministic validators
      - type: is-json
      - type: regex
        value: "\\S"   # non-empty output
      # LLM-as-judge assertions with blinded rubricPrompt and per-judge provider
      - description: accuracy by judge-1
        type: llm-rubric
        metric: accuracy
        weight: 25
        value: "Criterion: accuracy. Ensure all acceptance criteria are met."
        provider: openai:gpt-4.1-mini
        options:
          rubricPrompt:
            - role: system
              content: "Return JSON {reason, pass, score}; do not reveal hidden metadata."
            - role: user
              content: "TASK: {{vars.task_spec}}\nACCEPTANCE: {{vars.acceptance_criteria}}\nOUTPUT: {{output}}"
      - description: adherence by judge-2
        type: llm-rubric
        metric: adherence
        weight: 20
        provider: anthropic:messages:claude-sonnet-4-20250514
        value: "Criterion: adherence to instructions and acceptance criteria."
```

---

## 8) Docker & Local Run

```yaml
docker-compose.yaml (excerpt)

version: "3.8"
services:
  db:
    image: postgres:16-alpine
    environment:
      POSTGRES_USER: bench
      POSTGRES_PASSWORD: bench
      POSTGRES_DB: bench
    ports: ["5432:5432"]

  promptfoo:
    image: ghcr.io/promptfoo/promptfoo:latest  # official container
    entrypoint: ["sleep","inf"]                # used as a tool container
    environment:
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}

  bench:
    build: ./bench  # Python app (or image: ghcr.io/yourorg/bench:latest)
    volumes:
      - ./runs:/app/runs
    env_file: .env
    depends_on: [db, promptfoo]
```

The Python app invokes promptfoo either by shelling into the promptfoo container (e.g., docker exec) or by installing promptfoo in the same image. Container availability is supported via GHCR.

---

## 9) Observability & Compliance Details
- Config & Result Archival: Save the exact Promptfoo config and results for each run to support reproducibility & audit.
- Content Hashing: Hash the RunManifest, Promptfoo config, rubric files, and validator plug‑ins.
- No Chain‑of‑Thought: Judge prompts request scores + reasons, not raw internal chain‑of‑thought details.
- Audit Trail: Log retries/backoff, errors; track who/when triggered bench resume.

---

## 10) Open Questions & Required Stakeholder Inputs
1. Provider mapping: Confirm the exact target model IDs → Promptfoo provider strings (e.g., openai:gpt-4.1, anthropic:messages:claude-sonnet-4-20250514).
2. Judge pool models: Which judge models to use (and weights, if any)? We currently average or trimmed-mean across judges. (We can assign equal weights or per‑judge reliability weights.)
3. Replicates: Default --repeat value per run (3?) and whether to set seed across providers (not all providers support deterministic seeds; Replicate supports seed).
4. Confidence intervals: Prefer replicate SE @95% (simple) or bootstrap across tasks/inputs?
5. DB choice for local: Start with SQLite (simpler) vs Postgres (heavier but robust)?
6. Validators: Which deterministic checks are “critical” gates (JSON schema, regex patterns, length caps, toxicity/PII classifiers)?
7. Tie‑break policy: If CIs overlap & Δ<1, break ties by lower cost or higher validation pass‑rate as primary?
8. Minimal REST: Which endpoints to enable first (/runs, /runs/{id}/status, /runs/{id}/report) and any local auth needed?

---

## 11) Technical Specification Summary (README‑style)

### Purpose & Scope
Run prompt‑variant × LLM experiments with replication, automated validation, double‑blind LLM‑as‑Judge, weighted aggregation, and reproducible logs. Outputs include ranked per‑LLM recommendations and side‑by‑side tables.

### Key Components
- Orchestrator (Python): Reads RunManifest → expands grid → renders Promptfoo config → executes promptfoo eval → imports JSON → aggregates weighted scores + CIs → reports.
- Promptfoo Runner: Uses Promptfoo CLI (--output json, --repeat K, --grader optional) and llm-rubric assertions; captures latency/tokens/cost when available.
- Validators: Deterministic (regex/is‑json) within Promptfoo; optional Python validators (DeepEval/Ragas) for RAG and hallucination metrics.
- Judging: Model‑graded llm-rubric with blinded rubricPrompt and judge provider override per assertion for multi‑judge scoring.
- Storage: SQL (entities, indices) + local FS (configs, raw results, logs, Markdown/CSV/Parquet).

### Workflow
Ingest → Plan → Promptfoo Generate/Judge → Validate → Aggregate (weights, CI, ties) → Report → Finalize.

### Non‑Functional
- Determinism best‑effort (provider seeds where supported; record versions/params).
- Local Docker; minimal REST; small‑team friendly.
- Modular for plug‑ins (validators/judges/weights).
- Licenses: MIT/Apache‑2.0 only (Promptfoo MIT; DeepEval Apache‑2.0; TruLens MIT; Ragas Apache‑2.0).

---

## 12) Why this is the right fit
- Minimal ops: We keep Python as the system’s brain and rely on Promptfoo as a lightweight, robust execution & judging engine you can run locally (even via official container).
- Exact features you need: JSON outputs, replicates (--repeat), LLM‑as‑judge (llm-rubric), providers across families, and cost/latency capture.
- Flexibility: You retain full control over blinding, weights, statistics (CI/ties), and reporting, all reproducible from a RunManifest and archived configs/results.

---

## Appendix: Small “component responsibilities” table

| Component | Responsibilities |
|-----------|-----------------|
| Orchestrator | Manifest ingest; job graph expansion; randomization; Promptfoo config rendering; invoking Runner; results import; aggregation; reports; retries/backoff; audit logging. |
| Promptfoo Adapter | Generate config; set validators + llm-rubric with blind rubricPrompt; set providers/decoding params/seeds; call CLI; persist artifacts. |
| Validators | Deterministic (Promptfoo assertions) + Python metrics (DeepEval/Ragas) for specialty checks. |
| Judging | Promptfoo llm-rubric w/ per‑judge provider; multi‑judge scores; rationale capture. |
| Aggregator/Stats | Weighted means; SE/95% CI; tie logic (cost/pass‑rate). |
| Storage & Reporting | SQL persistence + FS artifacts; produce Markdown tables + CSV/Parquet exports. |

---

If you want, I can tailor the Promptfoo config generator to your actual Task and PromptVariant templates (placeholders, JSON schemas, regexes) and add a tiny docker exec wrapper so your Python container always calls the Promptfoo container consistently.