Perfect — thanks for sharing the full prompt.

Here’s the updated version transformed from Prompt-Engineering to Prompt-Eval, including a new rubric tailored to evaluation frameworks rather than authoring frameworks.

  

  

  

  

🧠 Research Prompt — Top 5 Active Prompt-Eval Frameworks (≥4k ⭐ on GitHub)

  

  

  

🎯 Role & Objective

  

  

You are a Senior Developer Research Analyst. Your mission is to identify, compare, and recommend the top 5 open-source prompt-evaluation (Prompt-Eval) frameworks that meet all criteria below. Focus only on frameworks with 4,000+ GitHub stars and recent activity (commits within the last 12 months) to ensure long-term viability.

  

  

✅ Inclusion Criteria

  

  

1. Evaluation Harness: Built-in support for running structured evaluations across prompts, models, and datasets.
2. Metrics & Rubrics: Native support for quantitative (BLEU, ROUGE, accuracy, etc.) and qualitative/LLM-as-judge rubrics.
3. Python SDK: First-class, stable, well-documented.
4. Experiment Tracking: Versioning of runs, scoring logs, diffs across prompt/model/output versions.
5. Local Deployment: Self-hostable; data stored under user control; avoid vendor lock-in.
6. Active GitHub Project:  
    

- ≥ 4,000 stars
- Last commit within 12 months

8.   
    

  

  

  

🚫 Exclusion Criteria

  

  

- SaaS-only or vendor-locked platforms.
- No Python SDK.
- Dormant repos or < 4k stars.

  

  

  

🔎 Research & Evidence

  

  

Use primary sources (official docs, READMEs, examples). For each framework capture: license, stars, last commit date, contributors, release cadence, docs depth, scoring/eval features, export/migration paths (e.g., results as JSON/CSV/SQLite/Parquet).

  

  

🧪 Evaluation Rubric (weights sum to 1)

  

  

- Evaluation Harness & Config Flexibility — 0.20
- Metrics & Rubric Support (quant + qual) — 0.20
- Python SDK Quality — 0.15
- Experiment Tracking & Versioning — 0.15
- Local Deployability & Data Control — 0.15
- GitHub Activity (Stars + Recency) — 0.10
- Extensibility (plugins/custom metrics) — 0.05

  

  

Score each criterion 0–5, multiply by weights, and compute a weighted total. Be explicit about trade-offs.

  

  

📦 Required Output (in order)

  

  

1. Shortlist Table (Top 5 Only)  
    Columns: Name | OSS License | GitHub Stars | Last Commit (ISO) | Python SDK | Eval Harness | Metrics/Rubrics | Tracking/Versioning | Local Deploy | Notable Gaps
2. Feature Matrix  
    Rows = features; Cols = frameworks. Include: dataset integration; batch eval; human vs LLM-judge support; built-in metrics; custom metric hooks; run logs; diffing; Git integration; export formats; dashboard/UI; reproducibility configs.
3. Scorecard (Rubric)  
    Provide JSON with per-framework scores, e.g.

  

[

  {

    "name": "FrameworkX",

    "scores": {

      "eval_harness": 4.5,

      "metrics_rubrics": 4.0,

      "python_sdk": 5.0,

      "tracking": 4.0,

      "local": 5.0,

      "github_activity": 5.0,

      "extensibility": 3.5

    },

    "weighted_total": 4.42

  }

]

  

3.   
    
4. Evidence & Links  
    For each framework: GitHub repo, docs, self-host guide, UI demos, scoring/rubric docs, export formats. Include stars and last commit date explicitly.
5. Recommendation  
    

- Best Overall for the target use case with rubric-tied rationale.
- Runner-up(s) and when to choose them.
- Adoption Plan: local deployment steps, suggested repo/file layout (e.g., evals/<dataset>/<config>.yaml + results DB), Git strategy, enabling diffs.
- Fallback/Migration: export/import of eval configs & results; keep vendor-neutral.

7.   
    

  

  

  

🧱 Constraints & Preferences

  

  

- Output in Markdown.
- Assume Linux/macOS local environments only (no cloud prerequisites).
- Prefer MIT/Apache-2.0 licenses when possible.
- Keep prompts/configs, metadata (YAML/JSON), and results (SQLite/Parquet) separate for clean versioning.

  

  

  

🧵 Sample Candidates (non-exhaustive; must still meet ≥4k ⭐ & activity)

  

  

- Promptfoo
- Promptflow
- LangChain Evaluation (LangSmith OSS-compatible parts)
- Haystack Eval  
    (Add/replace as appropriate, but enforce the ≥4k ⭐ and recency filters.)

  

  

  

✅ Validation

  

  

- Confirm the top 5 all meet ≥4k stars and recent commits.
- Confirm weights sum to 1 and rankings match weighted totals.
- Call out any requirements unmet by the winner and list OSS companions/workarounds.

  

  

  

  

Would you like me to also redesign the Feature Matrix template specifically for Prompt Eval (datasets, judges, metrics, reproducibility configs) instead of reusing the Prompt-Engineering one? That way the scoring and comparison sheet will be plug-and-play for eval.