---
title: "Sports Smartwatch Review – Hiking + Swimming Focus"
template_version: "1.0"
---

{# ===============================
Example input (YAML/JSON) you pass to render:
{
  "watch_name": "Garmin Fenix 7",
  "evaluator": "Sports Technology Analyst",
  "date": "2025-09-14",
  "context": "Primary use: hiking, indoor pool, open-water swimming",
  "scores": {
    "gps_acc": 9,
    "hr_acc": 8,
    "alt_nav": 9,
    "stroke_lap": 8,
    "nav_features": 10,
    "elevation_env": 8,
    "battery_hike": 9,
    "durability_hike": 9,
    "pool_accuracy": 8,
    "ow_gps": 8,
    "water_resistance": 9,
    "app_viz": 8,
    "integrations": 9,
    "value_ratio": 8
  }
}
=============================== #}

# {{ watch_name }}
**Evaluator:** {{ evaluator }}  
**Date:** {{ date }}  
**Use-Context:** {{ context }}

---

{% set W = {
  "gps_acc": 15, "hr_acc": 10, "alt_nav": 10, "stroke_lap": 5,
  "nav_features": 10, "elevation_env": 5, "battery_hike": 5, "durability_hike": 5,
  "pool_accuracy": 7, "ow_gps": 7, "water_resistance": 6,
  "app_viz": 5, "integrations": 5,
  "value_ratio": 5
} %}

{% macro pts(score, weight) -%}
  {{ ((score|float / 10.0) * weight)|round(2) }}
{%- endmacro %}

{% set S = scores %}

# 1) Core Performance (40%)
| Factor | Weight | Score (0–10) | Weighted Pts |
|---|---:|---:|---:|
| GPS Accuracy (Hiking & Open Water) | {{ W.gps_acc }} | {{ S.gps_acc }} | {{ pts(S.gps_acc, W.gps_acc) }} |
| Heart Rate Accuracy | {{ W.hr_acc }} | {{ S.hr_acc }} | {{ pts(S.hr_acc, W.hr_acc) }} |
| Altitude & Navigation Accuracy | {{ W.alt_nav }} | {{ S.alt_nav }} | {{ pts(S.alt_nav, W.alt_nav) }} |
| Stroke & Lap Detection (Pool) | {{ W.stroke_lap }} | {{ S.stroke_lap }} | {{ pts(S.stroke_lap, W.stroke_lap) }} |

{% set core_total = pts(S.gps_acc, W.gps_acc)|float
  + pts(S.hr_acc, W.hr_acc)|float
  + pts(S.alt_nav, W.alt_nav)|float
  + pts(S.stroke_lap, W.stroke_lap)|float %}

**Core Performance Subtotal:** **{{ core_total|round(2) }} / 40**

---

# 2) Hiking & Outdoor Usability (25%)
| Factor | Weight | Score (0–10) | Weighted Pts |
|---|---:|---:|---:|
| Navigation Features (maps, GPX, breadcrumb) | {{ W.nav_features }} | {{ S.nav_features }} | {{ pts(S.nav_features, W.nav_features) }} |
| Elevation & Environmental Adaptability | {{ W.elevation_env }} | {{ S.elevation_env }} | {{ pts(S.elevation_env, W.elevation_env) }} |
| Battery Life (Hiking Mode) | {{ W.battery_hike }} | {{ S.battery_hike }} | {{ pts(S.battery_hike, W.battery_hike) }} |
| Durability for Hiking (casing, buttons) | {{ W.durability_hike }} | {{ S.durability_hike }} | {{ pts(S.durability_hike, W.durability_hike) }} |

{% set hike_total = pts(S.nav_features, W.nav_features)|float
  + pts(S.elevation_env, W.elevation_env)|float
  + pts(S.battery_hike, W.battery_hike)|float
  + pts(S.durability_hike, W.durability_hike)|float %}

**Hiking & Outdoor Usability Subtotal:** **{{ hike_total|round(2) }} / 25**

---

# 3) Swimming-Specific Usability (20%)
| Factor | Weight | Score (0–10) | Weighted Pts |
|---|---:|---:|---:|
| Indoor Pool Accuracy (laps, stroke) | {{ W.pool_accuracy }} | {{ S.pool_accuracy }} | {{ pts(S.pool_accuracy, W.pool_accuracy) }} |
| Open-Water GPS Tracking | {{ W.ow_gps }} | {{ S.ow_gps }} | {{ pts(S.ow_gps, W.ow_gps) }} |
| Water Resistance & Durability | {{ W.water_resistance }} | {{ S.water_resistance }} | {{ pts(S.water_resistance, W.water_resistance) }} |

{% set swim_total = pts(S.pool_accuracy, W.pool_accuracy)|float
  + pts(S.ow_gps, W.ow_gps)|float
  + pts(S.water_resistance, W.water_resistance)|float %}

**Swimming-Specific Usability Subtotal:** **{{ swim_total|round(2) }} / 20**

---

# 4) Software & Ecosystem (10%)
| Factor | Weight | Score (0–10) | Weighted Pts |
|---|---:|---:|---:|
| App & Data Visualization | {{ W.app_viz }} | {{ S.app_viz }} | {{ pts(S.app_viz, W.app_viz) }} |
| Third-Party Integrations | {{ W.integrations }} | {{ S.integrations }} | {{ pts(S.integrations, W.integrations) }} |

{% set soft_total = pts(S.app_viz, W.app_viz)|float
  + pts(S.integrations, W.integrations)|float %}

**Software & Ecosystem Subtotal:** **{{ soft_total|round(2) }} / 10**

---

# 5) Value & Market Fit (5%)
| Factor | Weight | Score (0–10) | Weighted Pts |
|---|---:|---:|---:|
| Price-to-Performance (Hikers/Swimmers) | {{ W.value_ratio }} | {{ S.value_ratio }} | {{ pts(S.value_ratio, W.value_ratio) }} |

{% set value_total = pts(S.value_ratio, W.value_ratio)|float %}
**Value & Market Fit Subtotal:** **{{ value_total|round(2) }} / 5**

---

{% set grand_total = (core_total + hike_total + swim_total + soft_total + value_total)|round(2) %}

# ✅ Final Score
**Total:** **{{ grand_total }} / 100**

---

## Verdict
- **Strengths:** _Write concise, evidence-based strengths observed during testing (e.g., topo maps, long GPS battery, reliable pool laps)._  
- **Trade-offs:** _Note any compromises (e.g., smartwatch features, LTE, third-party app depth)._  
- **Best For:** _Hikers, open-water swimmers, triathletes; specify scenarios._

## Testing Notes (Methods & Evidence)
- Hiking: trails with canopy, mountain routes; reference GPX vs. gold-standard devices.
- Pool: 25m/50m lanes; interval sets; compare lap counts vs manual tally.
- Open-water: GPS drift recovery; sighting turns; compare to reference buoy/device.
- HR validation: chest strap comparison on climbs and intervals.

> _This review was generated with a standardized rubric emphasizing hiking and swimming performance. All factor scores are on a 0–10 scale; weighted points sum to 100._
