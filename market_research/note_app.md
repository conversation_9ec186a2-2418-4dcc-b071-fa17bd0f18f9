🎯 Task:
Research and evaluate the top 5 most popular note‑taking apps available on iPad and iOS (macOS is a bonus). Strictly exclude marketing fluff—focus on user reviews, Reddit threads, tech articles, and forums. Filter only those apps that:
	•	Support Markdown for note entry and storage.
	•	Allow bulk export/import or flexible synchronization to avoid vendor lock‑in.
	•	Preferably offer seamless automatic sync between iPad and Mac via cloud (e.g. iCloud, Dropbox), but this is optional.

📋 Requirements:
	1.	List exactly 5 apps, ranked by popularity among iPad/iOS users.
	2.	For each app, include:
	•	Markdown support and file format (e.g., raw .md).
	•	Export/import capabilities (bulk or per-note).
	•	Sync options and how user data is stored.
	•	Avoidance of vendor lock-in: e.g., plain‑text files, open‑source, open formats.
	•	User feedback—summaries from user reviews or forum discussions, not marketing.
	3.	Provide web citations for each claim.
	4.	No promotional bias or speculative recommendations—purely factual, user‑reported experiences.

⸻

📝 Example Output Structure (for one app):

⸻

1. Bear (iOS / iPadOS / macOS)
	•	Markdown support: Full inline support; native .md and TextBundle export  ￼ ￼ ￼ ￼
	•	Export/import: Bulk import from Evernote or files; batch export to Markdown, HTML, PDF, etc.  ￼ ￼
	•	Sync & storage: Uses iCloud for auto‑sync across Apple devices; note data in proprietary database but export is open  ￼
	•	Vendor lock‑in: Exports plain Markdown; no proprietary format dependency  ￼
	•	User feedback:
“Bear is an impressive notes app… functions very well on all Apple devices” —App Store review  ￼

⸻

Repeat similar profiles for the remaining four apps 