# 🧠 Market Research AI Agent – Cycling Glasses Comparative Analysis (Global, All Languages)

**Inputs**
- `glasses_list`: {{ glasses_list | default(["Example Model A","Example Model B","Example Model C"]) | tojson }}
- `time_window_months`: {{ time_window_months | default(12) }}
- `weights` (0–1, sum≈1): {{ weights | default({"wind_protection":0.35,"rain_protection":0.30,"glare_reduction":0.25,"multisport":0.10}) | tojson }}
- `scale_max`: {{ scale_max | default(100) }}
- `min_reviews_per_model`: {{ min_reviews_per_model | default(20) }}
- `translate_to`: {{ translate_to | default("English") }}
- `region_focus` (optional): {{ region_focus | default("Global") }}

> **Note**: The provided `glasses_list` is an example. Use whatever list the user supplies.

---

## 🎯 Objective
Compare the following cycling glasses for **multi-sport use (primarily cycling)** with emphasis on **strong winds**, **rain**, and **glare reduction at dusk/evening/night**. Recommend the **single best option** based on **unbiased user reviews** gathered from the internet **without restricting sources or languages**. All reporting must be in **{{ translate_to }}**.

**Models to analyze**
{% for m in glasses_list %}
- {{ m }}
{% endfor %}

---

## 🔍 Research Scope & Method

1. **Sources (no restrictions):** Search the **entire internet**. Include marketplaces and retailers (e.g., regional e-commerce), forums/communities (e.g., cycling subreddits, local-language boards), independent blogs, review aggregators, news sites, brand stores with **verified purchase** indicators, and regional outlets. **Do not** limit to any predefined list.

2. **Language Policy:** Ingest reviews in **all languages**. For analysis and output, **translate** everything to **{{ translate_to }}** while retaining:
   - Original language and locale tag in the evidence log
   - Any context (e.g., weather, road type) mentioned in the source language

3. **Bias Filtering (keep only unbiased, experience-based reviews):**
   - Exclude posts with clear promotional/affiliate intent, “gifted product,” or sponsored disclosures.
   - De-duplicate syndicated/SEO farm content; keep the earliest canonical instance.
   - Prefer verified purchases or authors with consistent, mixed-history reviews.
   - Prioritize **situational detail** (wet/windy commutes, night rides, descents, lens variant used).

4. **Inclusion Rule:** Score a model only if **≥ {{ min_reviews_per_model }}** relevant reviews remain **after** filtering (across all languages).

5. **Evidence Capture:** For each usable review, record:
   - Weather/time context (wind speed if given, rain intensity, dusk/night)
   - Lens variant (clear/photochromic/polarized/AR coatings)
   - Fit/coverage/seal & fogging notes
   - Original language & domain
   - Short, paraphrased takeaway (no long quotes)

---

## 🧮 Scoring Rubric ({{ scale_max }}-point scale)

- **Wind Protection ({{ (weights.wind_protection*100) | round(0) }}%)**  
  Shielding against **strong crosswinds/headwinds**; dryness/irritation reports; frame coverage & seal at speed.

- **Rain Protection ({{ (weights.rain_protection*100) | round(0) }}%)**  
  Ability to keep **rain out of eyes**; beading/runoff behavior; compatibility with caps/hoods; stability in downpours.

- **Glare Reduction ({{ (weights.glare_reduction*100) | round(0) }}%)**  
  Reduction of **headlight/traffic-light glare** in **evening/night**; haloing/flare; effectiveness of AR/anti-glare coatings, photochromic responsiveness.

- **Multi-Sport Usability ({{ (weights.multisport*100) | round(0) }}%)**  
  Comfort, stability, sweat/fog handling across other sports (running, hiking); lens swap ease if relevant.

**Overall Score** =  
`(Wind * {{ weights.wind_protection }}) + (Rain * {{ weights.rain_protection }}) + (Glare * {{ weights.glare_reduction }}) + (MultiSport * {{ weights.multisport }})`

---

## 🧰 Data Extraction & Normalization
- Normalize qualitative evidence to factor-specific 0–{{ scale_max }} subscores.
- Aggregate **cross-language** evidence fairly; do not overweight English sources.
- Map repeated complaints/praise to confidence-adjusted scores (e.g., frequent fogging → penalty).
- Sentiment categories: **Positive / Mixed / Negative** with a one-line rationale (paraphrased).

---

## 📊 Comparison Table
| Model | Wind Protection (0–{{ scale_max }}) | Rain Protection (0–{{ scale_max }}) | Glare Reduction (0–{{ scale_max }}) | Multi-Sport (0–{{ scale_max }}) | **Overall (0–{{ scale_max }})** | Overall Sentiment | Source Diversity (#Domains) | Top Languages Seen | Review Highlights (bulleted) |
|---|---:|---:|---:|---:|---:|---:|---:|---|---|
{% for m in glasses_list -%}
| {{ m }} | {{ "__WIND_"+loop.index|string+"__" }} | {{ "__RAIN_"+loop.index|string+"__" }} | {{ "__GLARE_"+loop.index|string+"__" }} | {{ "__MULTI_"+loop.index|string+"__" }} | {{ "__OVERALL_"+loop.index|string+"__" }} | {{ "__SENTIMENT_"+loop.index|string+"__" }} | {{ "__DOMAINS_"+loop.index|string+"__" }} | {{ "__LANGS_"+loop.index|string+"__" }} | {{ "__NOTES_"+loop.index|string+"__" }} |
{% endfor %}

> Replace placeholders (`__WIND_i__`, `__RAIN_i__`, etc.) with computed scores and synthesized notes from filtered reviews.

---

## 📝 Evidence Digest (Per Model)
{% for m in glasses_list %}
### {{ m }}
- **Wind Protection:** {{ "__EVIDENCE_WIND_"+loop.index|string+"__" }}
- **Rain Protection:** {{ "__EVIDENCE_RAIN_"+loop.index|string+"__" }}
- **Glare Reduction (Evening/Night):** {{ "__EVIDENCE_GLARE_"+loop.index|string+"__" }}
- **Multi-Sport Usability:** {{ "__EVIDENCE_MULTI_"+loop.index|string+"__" }}
- **Fit/Seal & Coverage Notes:** {{ "__EVIDENCE_FIT_"+loop.index|string+"__" }}
- **Fogging/Coating Durability:** {{ "__EVIDENCE_FOG_"+loop.index|string+"__" }}
- **Top Sources (title • domain • date • original language):** {{ "__SOURCES_"+loop.index|string+"__" }}
{% endfor %}

---

## ✅ Recommendation
**Top Pick:** {{ "__TOP_MODEL__" }}

**Why this choice**
- **Wind:** {{ "__WHY_WIND__" }}
- **Rain:** {{ "__WHY_RAIN__" }}
- **Glare:** {{ "__WHY_GLARE__" }}
- **Multi-Sport:** {{ "__WHY_MULTI__" }}

**Trade-offs / Caveats**
- {{ "__TRADEOFFS__" }}

**Runner-Up (if within {{ (scale_max*0.05) | round(0) }} pts):** {{ "__RUNNER_UP__" }} — {{ "__RUNNER_UP_REASON__" }}

---

## 📎 Methodological Notes
- No source restrictions; **all-language** intake, reported in **{{ translate_to }}**.
- Scores normalized to {{ scale_max }}; weighted per rubric above.
- Cross-language de-duplication and balanced weighting to avoid English-language bias.
- Only **experience-based**, non-promotional reviews included; marketing claims ignored.
- Time-bounded to last **{{ time_window_months }} months** (older reviews used only to confirm persistent issues).