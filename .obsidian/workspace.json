{"main": {"id": "31442ae46bba0e03", "type": "split", "children": [{"id": "de886baf235536f7", "type": "tabs", "children": [{"id": "98505b316816831b", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "New tab"}}]}], "direction": "vertical"}, "left": {"id": "cf1209d883d38193", "type": "split", "children": [{"id": "9b00be5a9969e96b", "type": "tabs", "children": [{"id": "51548bcc8062493e", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "90464ef68c45f75c", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "227f56949fb92975", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "d4ff872009e64003", "type": "split", "children": [{"id": "740237ddf0b5792d", "type": "tabs", "children": [{"id": "90a7d900a736a80e", "type": "leaf", "state": {"type": "backlink", "state": {"file": "coding/architect/airbnb_arch.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for airbnb_arch"}}, {"id": "648061b9ae2a8f60", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "coding/architect/airbnb_arch.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from airbnb_arch"}}, {"id": "eec19e71ac209a0a", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "b4e2ae87aca62c91", "type": "leaf", "state": {"type": "outline", "state": {"file": "coding/architect/airbnb_arch.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of airbnb_arch"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "51548bcc8062493e", "lastOpenFiles": ["agents/technical_lead_arch_decompisition.md", "market_research/water_tests_prompt.md", "coding/architect/airbnb_multi_agent_app_prompt.md", "coding/ai/claude-rules.md", "reviews/service_review.md", "reviews/service_comparison.md", "reviews/service_comparision.md", "market_research/reusable_packaging.md", "market_research/note_app.md", "market_research", "market_research.md", "misc/india_health_insurance.md", "coding/architect/multi_agent_chat_prompt.md", "coding/data_analysis/spot_price_prompt.md", "coding/data_analysis", "coding/code_review/qa_agent_gemini.md", "coding/code_review/qa_agent.md", "devops/local_backup.md", "coding/code_review/code_review_agent.md", "reviews/bicycle_research.md", "reviews/product_review.md", "health/cycling_posture_study.md", "health", "reviews/business_banking.md", "agents/assistant_messaging_prompt.md", "agents/assistant_prompt_messages.md", "agents/assitant_prompt_gemini25_pro.md", "agents/assistant_prompt_gpt4.md", "agents/assitant_prompt.md", "review_analysis_negative.txt", "review_analysis_positive.txt", "welcome_template.txt"]}