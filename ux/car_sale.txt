# Car on sale web page
# please create a wireframe for a web page that shows a car for sale.
# the page should have a header with the title of the car for sale.
# the page should have a main section with the car for sale.
# main section will display the car details followed by images of car.
# the page should have a footer with the contact information.


# Refined
Create a responsive web page that displays a car for sale using sveltekit and shadcn/ui.

The web page should include clearly defined sections to highlight the features and provide contact details for a potential sale. Specifically:

- The header should clearly state the title of the car that is being offered for sale.
- The main content area should give detailed information about the car, followed by a series of images showcasing various aspects of the vehicle.
- The footer should contain contact information relevant for the car sale.

# Sections to Include

**1. Header**  
   - Title with the name of the car model, make, and year.
   - Ensure it's visually prominent, giving the visitor a clear first impression.

**2. Main Section**  
   - **Car Details**:  
     Include information such as:
     - Car Make/Model  
     - Year of Manufacture  
     - Mileage  
     - Price  
     
   - **Images**:
     - Display gallery ofmultiple car images:
     - When clicked, the image should open as a popup slideshow with a x button to close.

   
**3. Footer**  
   - **Contact Information**:
     - Contact Name
     - Phone Number
     - WhatsApp link

# Steps

1. **Design the Header**  
   Place a title prominently, use large fonts, and ensure it's easily visible.

2. **Draft the Main Section Layout**  
   - First add a details section with labeled fields describing aspects of the car.
   - Subsequently, add an image gallery underneath the information to allow for both vertical scrolling and potentially a click-through slideshow for images.

3. **Construct the Footer**  
   Give sufficient space for contact details, ensuring easy readability.

# Notes

- Placeholder values will need to be replaced with actual data by the user.
- The goal is to make the layout informative yet minimal so that users can quickly access relevant information.
- Ensure a mobile-friendly design is possible with these elements, suggesting responsiveness where needed.