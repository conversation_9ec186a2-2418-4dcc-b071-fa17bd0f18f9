{# ================================
 Sports Scientist AI Agent – CGM Comparison Template (.j2.md)
 Purpose: Compare two CGMs for athlete metabolism, sleep, nutrition & performance insights
================================ #}

{# -------- Inputs (override as needed) -------- #}
{% set devices = devices | default(["FreeStyle Libre 3", "FreeStyle Libre 3 Plus"]) %}
{% set sport = sport | default("endurance cycling") %}
{% set athlete_level = athlete_level | default("competitive amateur") %}
{% set context = context | default("high-volume training with mixed intensities; heat & sweat exposure; travel blocks") %}
{% set focus_factors = focus_factors | default([
  "Metabolic profiling (glucose variability, time-in-range, excursions)",
  "Food–glucose dynamics (pre/post-exercise meals, fueling strategies)",
  "Sleep quality & nocturnal glucose patterns",
  "Training block monitoring (intensity days vs recovery)",
  "Practicality for athletes (adhesion, comfort, reliability, latency)",
  "Data access for analytics (export, API, schema, cadence)"
]) %}
{% set time_window = time_window | default("last 12 months") %}
{% set markets = markets | default(["UK", "EU"]) %}
{% set output_style = output_style | default("table-first") %}

{# Scoring rubric (0–5); adjust weights per project) #}
{% set rubric = {
  "Accuracy_during_rapid_change": 0.20,
  "Latency_realtime_responsiveness": 0.15,
  "Wearability_adhesion_sweat": 0.15,
  "Sensor_lifespan_uptime": 0.10,
  "Data_integration_exports_API": 0.20,
  "App_features_alerts_notes": 0.05,
  "Cost_availability_support": 0.05,
  "Durability_water_contact": 0.05,
  "User_privacy_controls": 0.05
} %}

{# Optional known constraints the agent should honor #}
{% set constraints = constraints | default([
  "Rely on information that is current within " ~ time_window,
  "If uncertain, note uncertainty explicitly; do not invent specs",
  "Focus on athlete-relevant evidence (exercise, sweat, motion, latency)",
  "Do not give medical advice; stick to device comparison and data utility"
]) %}

# 🏅 Sports Scientist AI Agent – CGM Comparison

## 1) Objective
Compare **{{ devices[0] }}** vs **{{ devices[1] }}** for a **{{ athlete_level }} {{ sport }}**.  
Purpose: select the CGM that best supports **metabolic understanding**, **food–sleep–performance** linkages, and **data science analysis** for dashboarding and exploratory pattern discovery.

**Athlete context:** {{ context }}

**Focus factors:**  
{% for f in focus_factors -%}
- {{ f }}
{% endfor %}

**Markets of interest:** {{ markets | join(", ") }}  
**Recency requirement:** Use sources and specs within **{{ time_window }}**.

---

## 2) Quick Checklist (what this analysis covers)
- Build an athlete-focused **comparison matrix** of {{ devices[0] }} vs {{ devices[1] }}  
- Evaluate **accuracy under rapid glycemic change**, **latency**, **wearability**, **lifespan**  
- Assess **data access** (export/API), **app ecosystem**, **privacy**  
- Consider **adhesion, sweat, water exposure, contact risk** during sport  
- Provide **weighted rubric scoring** and a **clear recommendation**

---

## 3) Comparison Matrix (Athlete-Relevant)
| Feature | {{ devices[0] }} | {{ devices[1] }} | Why it matters for athletes |
|---|---|---|---|
| Sensor lifespan (days) | {{ "<fill>" }} | {{ "<fill>" }} | Longer windows reduce sensor swaps mid-block |
| Warm-up time | {{ "<fill>" }} | {{ "<fill>" }} | Faster start simplifies race/travel days |
| Accuracy (overall MARD/metrics) | {{ "<fill>" }} | {{ "<fill>" }} | Better accuracy → cleaner trends & thresholds |
| Accuracy under rapid change (exercise) | {{ "<fill>" }} | {{ "<fill>" }} | High-intensity work needs fidelity during swings |
| Latency / real-time responsiveness | {{ "<fill>" }} | {{ "<fill>" }} | Lower lag = better fueling & hypo prevention |
| Adhesion & sweat resistance | {{ "<fill>" }} | {{ "<fill>" }} | Reliability during heat, sweat, friction |
| Water resistance (pool, rain) | {{ "<fill>" }} | {{ "<fill>" }} | Triathlon, swim, wet conditions |
| Physical profile (size/comfort) | {{ "<fill>" }} | {{ "<fill>" }} | Comfort under kit; reduced snag risk |
| App features (alerts, tagging meals/workouts) | {{ "<fill>" }} | {{ "<fill>" }} | Better context for analysis & interventions |
| Data export (CSV, JSON) | {{ "<fill>" }} | {{ "<fill>" }} | Essential for data science pipelines |
| API/SDK access (official/third-party) | {{ "<fill>" }} | {{ "<fill>" }} | Automation, integration, reproducibility |
| Sampling cadence / resolution | {{ "<fill>" }} | {{ "<fill>" }} | Higher resolution improves event alignment |
| Battery/sensor reliability | {{ "<fill>" }} | {{ "<fill>" }} | Reduces data gaps |
| Compatibility (iOS/Android, watches) | {{ "<fill>" }} | {{ "<fill>" }} | Field usability, notifications |
| Cost & availability ({{ markets | join(", ") }}) | {{ "<fill>" }} | {{ "<fill>" }} | Budget planning; replacement friction |
| Regulatory/indications (sport use) | {{ "<fill>" }} | {{ "<fill>" }} | Compliance, insurance, team protocols |
| Privacy controls & data ownership | {{ "<fill>" }} | {{ "<fill>" }} | Governance & ethical analytics |

> **Note:** If spec certainty is low, add “(uncertain)” and explain.

---

## 4) Athlete-Specific Considerations
- **Fueling experiments:** Pre/intro/post-session meal tags; detect **glucose rise time**, **peak**, **area-under-curve**, **return-to-baseline**.  
- **Session dynamics:** Align with **power/HR/RPE** to examine **glycemic lag** and **variability** during intervals.  
- **Recovery & sleep:** Overnight **time-in-range**, **nocturnal hypoglycemia**, **dawn phenomenon**; relate to **sleep stages** if available.  
- **Environment:** Heat/humidity → sweat/adhesion risk; travel → airport security, sensor turnover.  
- **Contact risk:** Crashes, jersey friction; profile height matters.  

---

## 5) Data Access for the Sports Data Scientist
**Required outputs & schema expectations:**
- **Timestamped glucose** at device-native cadence (include timezone & DST-safe ISO8601)
- **Event markers:** meals, carbs, caffeine, sessions (type, duration, intensity), recovery modalities
- **Export methods:** CSV/JSON pull, periodic batch export, or API with pagination & rate limits
- **Metadata:** sensor ID, calibration status (if any), errors/data gaps, firmware version
- **Governance:** consent log, anonymization options, retention policy

| Capability | {{ devices[0] }} | {{ devices[1] }} | Notes |
|---|---|---|---|
| Manual export (CSV/JSON) | {{ "<fill>" }} | {{ "<fill>" }} |  |
| Automated export (scheduled) | {{ "<fill>" }} | {{ "<fill>" }} |  |
| Official API / SDK | {{ "<fill>" }} | {{ "<fill>" }} |  |
| Third-party ecosystem | {{ "<fill>" }} | {{ "<fill>" }} |  |
| Webhooks / integrations | {{ "<fill>" }} | {{ "<fill>" }} |  |
| Data dictionary availability | {{ "<fill>" }} | {{ "<fill>" }} |  |

---

## 6) Weighted Scoring (0–5 per device; multiply by weights)
Weights (sum=1.0):  
{% for k, w in rubric.items() -%}
- **{{ k }}**: {{ '%.2f'|format(w) }}
{% endfor %}

| Criterion | Weight | {{ devices[0] }} Score (0–5) | {{ devices[1] }} Score (0–5) | Rationale |
|---|---:|---:|---:|---|
{% for crit, w in rubric.items() -%}
| {{ crit | replace("_"," ") }} | {{ '%.2f'|format(w) }} | {{ "<score>" }} | {{ "<score>" }} | {{ "<why>" }} |
{% endfor %}

**Total ({{ devices[0] }}) =** Σ(weight × score) → **{{ "<compute>" }}**  
**Total ({{ devices[1] }}) =** Σ(weight × score) → **{{ "<compute>" }}**

> If totals are within 5%, prefer the device with **better data access** and **lower latency** for athlete experiments.

---

## 7) Expert Commentary (Concise Bullets)
- {{ devices[0] }}: {{ "<2–4 athlete-relevant strengths>" }}  
- {{ devices[1] }}: {{ "<2–4 athlete-relevant strengths>" }}  
- Risks/uncertainties: {{ "<list spec gaps, regional constraints, app quirks>" }}

---

## 8) Recommendation (2–3 Sentences)
**Recommendation:** {{ "<Choose one> – concise justification focusing on exercise accuracy/latency, reliability in sweat/heat, and data pipeline suitability." }}  
**If-not:** If {{ devices[0] }} / {{ devices[1] }} is unavailable or underperforms in field tests, select the alternative and adjust fueling/sleep analytics accordingly.

---

## 9) Implementation Notes for the Data Team
- Standardize timestamps to UTC; store **raw + processed** streams; track **sensor epochs** for seamless joins.  
- Derive: **TIR**, **CV**, **GV**, **MAGE**, **iAUC**, **time-to-peak**, **return-to-baseline**, **overnight nadir**, **pre/post session deltas**.  
- Join with: **training logs**, **power/HR**, **sleep metrics**, **meal annotations**.  
- Flag **data gaps** and **lag estimates** per session for model robustness.

---

## 10) Constraints & Disclaimers
{% for c in constraints -%}
- {{ c }}
{% endfor %}
