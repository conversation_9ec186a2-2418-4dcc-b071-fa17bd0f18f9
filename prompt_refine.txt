please refine following prompt to make it more specific and clear.
please only return the refined prompt as markdown file and nothing else.

prompt:
Please write Python code to extract JsonSchema xpaths which can be used in Crawl4AI to scrape data from a webpage.
For given Pydantic class, code should generate xpaths for each field in the class. 
**Instructions:**
- Convert Pydantic class to JsonSchema
- Extract xpaths for each field in the JsonSchema from the html
- Append the xpaths to the JsonSchema
- Return the JsonSchema with xpaths
**Implementation:**
- Use Crawl4AI to scrape the webpage for the given url
- Use crawl4ai strategy to extract xpaths

# Reference:
Here's the crawl4ai documentation, please refer to it for more details: 
 - [Configuration](https://docs.crawl4ai.com/core/browser-crawler-config/)
 - [Crawling](https://docs.crawl4ai.com/core/simple-crawling/)
 - [Result](https://docs.crawl4ai.com/core/crawler-result/)
 - [LLM Integration](https://docs.crawl4ai.com/extraction/llm-strategies/)

**Strict guidelines:**
- use only the above documentation to implement the code
- do not use any other libraries or tools
- do not hardcode the xpaths, use the crawl4ai to extract the xpaths
- do not use any other documentation or code snippets on crawl4ai usage






