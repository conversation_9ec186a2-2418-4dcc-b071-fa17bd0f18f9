# 🧪 Early Feedback Analysis for {{service_name}}

## 🎯 Objective  
You are a **Feedback Analysis AI Agent**. Research and analyze **early user feedback on the {{service_name}}.

---

## 🔍 Research Scope

1. **Gather Sources**  
   - Search for **user discussions, reviews, benchmarks, and blog posts** about **{{service_name}}**.  
   - Include data from:
     - GitHub (issues, stars, forks)  
     - Social media (Twitter/X, Reddit) and AI forums  
     - Hugging Face discussions and community blogs

2. **Summarize Feedback**  
   - Categorize **common positives** and **common negatives**  
   - Note any **direct comparisons** with other LLMs (e.g., GPT-4, Claude 3, Gemini)

3. **Identify Trends**  
   - Usage patterns: coding, reasoning, multilingual support  
   - Quality issues: hallucinations, latency, tool integration, fine-tuning  
   

4. **Developer-Centric Analysis**  
   - Performance on programming tasks:
     - Code generation (Python, JavaScript, etc.)  
     - Code explanation and debugging  
     - API usage workflows  
     - Agent-style reasoning (ReAct, AutoGen)

---

## 🧾 Output Format

```markdown
## 📝 Early Feedback Summary for {{service_name}}

### ✅ Strengths
- …

### ❌ Weaknesses
- …

### 🔧 Developer-Centric Findings
- …

### 📈 Key Trends
- …

### 🔗 References
1. [Title – URL]  
2. [Title – URL]  