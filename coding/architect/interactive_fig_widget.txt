# Interactive Figure Widget
Please architect a jupyter notebook widget that allows a user to interact with a figure using multiple options and different backend libraries.
For eg:
plot_type can be bar, line, scatter, etc. 
x_axis can be a column in the dataframe
y_axis can be a column in the dataframe
color can be a column in the dataframe

The widget should be able to use different backend libraries like matplotlib, seaborn, plotly, etc.
Purpose of the widget is to allow a user to explore the data and understand the relationship between the variables easily.






