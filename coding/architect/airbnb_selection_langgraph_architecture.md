## Airbnb Selection LangGraph Architecture

### Checklist (high‑level)
- Model the pipeline as a StateGraph with typed state and clean reducers.
- Add HITL checkpoints using `interrupt(...)` after Search and after Filtering.
- Fan‑out in parallel to Summarization and Rating; fan‑in with a guarded merge.
- Compile with a checkpointer for persistence; expose simple run/continue patterns.
- Keep agent logic modular so you can swap providers or add agents (e.g., PriceTrendAgent).

---

### 1) System Architecture (LangGraph view)

```mermaid
graph TD
  START([START]) --> S[Search Node]
  S -->|cond: mode==hitl?| H1[HITL A - review criteria]
  S -->|else| C[Coding Node]
  H1 --> C

  C --> F[Filtering Node]
  F -->|cond: mode==hitl?| H2[HITL B - review filters]
  F -->|else| SUM
  F -->|else| RATE
  H2 --> SUM
  H2 --> RATE

  SUM[Summarization Node] --> M[Merge/Score Apply]
  RATE[Rating base Node] --> M
  M -->|when both ready| A[Advisor Node -> Markdown]
  A --> <PERSON><PERSON>([END])
```

> Validation: Diagram mirrors the original agents with conditional (HITL) edges and a parallel fan‑out/fan‑in; proceed.

---

### 2) Why LangGraph here (in one breath)
- **HITL via interrupts**: pause nodes, persist state, then resume with user input; requires a checkpointer.
- **Parallel branches & joins**: natural fan‑out/fan‑in with regular/conditional edges.
- **Conditional routing**: skip or enter HITL nodes based on mode.
- **Persistence**: use an in‑memory/SQLite/Postgres checkpointer; `MemorySaver`/`InMemorySaver` are turnkey.

> Validation: Framework choice matches stateful DAG + HITL + parallelism; proceed.

---

### 3) LangGraph Scaffold (Python)

Drop‑in skeleton you can paste into a module. It reuses the earlier agent logic but mounts it as LangGraph nodes with HITL checkpoints and a parallel section.

```python
"""
LangGraph scaffold for the Airbnb Selection multi-agent pipeline.

Requires:
  pip install langgraph langgraph-checkpoint  # plus your LLM/lib deps as needed

Notes:
- HITL uses `interrupt(...)`; compile with a checkpointer (MemorySaver/InMemorySaver).
- Parallelism: filtering -> (summarize | rate) fan-out; merge waits until both present.
- Keep providers & logic modular; everything below is framework glue + light logic.
"""

from __future__ import annotations
from typing import Any, Dict, List, Mapping, Tuple, Literal
from typing_extensions import TypedDict, Annotated
from dataclasses import dataclass
from datetime import datetime
import math

# LangGraph bits
from langgraph.graph import StateGraph, START, END
from langgraph.types import interrupt, Command
# You can also use InMemorySaver; both are common in docs.
from langgraph.checkpoint.memory import MemorySaver  # or: InMemorySaver

# ----------------------------
# Data contracts (condensed)
# ----------------------------

class Location(TypedDict, total=False):
    city: str; country: str; lat: float; lon: float

class Price(TypedDict, total=False):
    per_night: float; currency: str; cleaning_fee: float; total_estimate: float

class Review(TypedDict, total=False):
    rating: float; date: str; text: str; reviewer_name: str

class Property(TypedDict, total=False):
    id: str; title: str; url: str
    location: Location; price: Price
    bedrooms: int; bathrooms: float; accommodates: int
    property_type: str; amenities: List[str]
    reviews: List[Review]
    derived: Dict[str, Any]

class AmenityPrefs(TypedDict, total=False):
    must_have: List[str]; nice_to_have: List[str]

class PriceRange(TypedDict, total=False):
    min: float; max: float

class DateRange(TypedDict, total=False):
    checkin: str; checkout: str

class PrefWeights(TypedDict, total=False):
    price: float; amenities: float; bedrooms: float; sentiment: float

class UserPreferences(TypedDict, total=False):
    location: str | Location
    date_range: DateRange
    guests: int
    bedrooms_min: int
    price_range: PriceRange
    amenities: AmenityPrefs
    other_constraints: Dict[str, Any]
    weights: PrefWeights
    sort_by: Literal["score", "price", "distance"]
    max_distance_km: float

class ScoreBreakdown(TypedDict, total=False):
    price: float; amenities: float; bedrooms: float; sentiment: float

# ----------------------------
# State (with simple reducers)
# ----------------------------

def _merge_dicts(a: Dict[str, Any] | None, b: Dict[str, Any] | None) -> Dict[str, Any]:
    out: Dict[str, Any] = {}
    if a: out.update(a)
    if b: out.update(b)
    return out

class GraphState(TypedDict, total=False):
    # control
    mode: Literal["autonomous", "hitl"]
    # inputs
    prefs: UserPreferences
    # stages
    raw_results: List[Dict[str, Any]]
    structured: List[Property]
    filtered: List[Property]
    summaries: Annotated[Dict[str, Dict[str, Any]], _merge_dicts]
    base_scores: Annotated[Dict[str, ScoreBreakdown], _merge_dicts]
    final_scores: Annotated[Dict[str, Tuple[float, ScoreBreakdown]], _merge_dicts]
    # output
    report_md: str

# ----------------------------
# Provider (stub) & utility
# ----------------------------

class ListingProvider:
    def __init__(self, items: List[Dict[str, Any]]): self.items = items
    async def search(self, preferences: UserPreferences) -> List[Dict[str, Any]]:
        return self.items  # swap with real API/scraper

def _dedupe(props: List[Property]) -> List[Property]:
    seen, out = set(), []
    for p in props:
        pid = p.get("id")
        if pid and pid not in seen:
            seen.add(pid); out.append(p)
    return out

# ----------------------------
# Agent-ish helpers (logic kept small)
# ----------------------------

def _coding(raw: List[Dict[str, Any]]) -> List[Property]:
    out: List[Property] = []
    for item in raw:
        try:
            out.append(Property(
                id=str(item.get("id") or item["listing_id"]),
                title=item.get("title", ""), url=item.get("url", ""),
                location=item.get("location", {}), price=item.get("price", {}),
                bedrooms=int(item.get("bedrooms", 0)), bathrooms=float(item.get("bathrooms", 0)),
                accommodates=int(item.get("accommodates", 1)),
                property_type=item.get("property_type", "apartment"),
                amenities=list(item.get("amenities", [])),
                reviews=list(item.get("reviews", [])),
                derived={}
            ))
        except Exception:
            continue
    return out

def _filtering(props: List[Property], prefs: UserPreferences) -> List[Property]:
    pr = prefs.get("price_range", {"min": 0, "max": float("inf")})
    min_price, max_price = float(pr.get("min", 0)), float(pr.get("max", float("inf")))
    bedrooms_min = int(prefs.get("bedrooms_min", 1))
    guests = int(prefs.get("guests", 1))
    must_have = set(map(str.lower, prefs.get("amenities", {}).get("must_have", [])))
    max_dist = prefs.get("max_distance_km")

    def ok(p: Property) -> bool:
        price = float(p.get("price", {}).get("per_night", float("inf")))
        if not (min_price <= price <= max_price): return False
        if int(p.get("bedrooms", 0)) < bedrooms_min: return False
        if int(p.get("accommodates", 1)) < guests: return False
        ams = set(map(str.lower, p.get("amenities", [])))
        if not must_have.issubset(ams): return False
        if max_dist is not None:
            dist = p.get("derived", {}).get("distance_km")
            if dist is not None and dist > float(max_dist): return False
        return True

    return [p for p in props if ok(p)]

_POS = {"clean", "spacious", "friendly", "walkable", "quiet", "great", "amazing", "comfortable"}
_NEG = {"dirty", "noisy", "rude", "small", "bad", "terrible", "uncomfortable", "far"}

def _summarize(props: List[Property]) -> Dict[str, Dict[str, Any]]:
    out: Dict[str, Dict[str, Any]] = {}
    for p in props:
        texts = " ".join([r.get("text", "") for r in p.get("reviews", [])])[:8000].lower()
        pos = sum(w in texts for w in _POS); neg = sum(w in texts for w in _NEG)
        sentiment = 0.5 if (pos + neg) == 0 else max(0.0, min(1.0, pos / (pos + neg)))
        aspects = {
            "cleanliness": texts.count("clean"),
            "location": texts.count("location") + texts.count("walk"),
            "host": texts.count("host") + texts.count("friendly"),
        }
        out[p["id"]] = {
            "review_summary": f"Cleanliness: {aspects['cleanliness']}; Location: {aspects['location']}; Host: {aspects['host']}.",
            "sentiment": sentiment, "aspects": aspects
        }
    return out

def _base_scores(props: List[Property], prefs: UserPreferences) -> Dict[str, ScoreBreakdown]:
    pr = prefs.get("price_range", {"min": 0, "max": float("inf")})
    min_price, max_price = float(pr.get("min", 0)), float(pr.get("max", float("inf")))
    bedrooms_min = int(prefs.get("bedrooms_min", 1))
    must_have = set(map(str.lower, prefs.get("amenities", {}).get("must_have", [])))

    def norm(x: float, lo: float, hi: float) -> float:
        if hi <= lo: return 1.0
        return max(0.0, min(1.0, (x - lo) / (hi - lo)))

    out: Dict[str, ScoreBreakdown] = {}
    for p in props:
        price = float(p.get("price", {}).get("per_night", max_price * 2))
        price_score = max(0.0, 1.0 - norm(price, min_price, max_price))
        ams = set(map(str.lower, p.get("amenities", [])))
        amenities = 1.0 if must_have.issubset(ams) else len(must_have & ams) / (len(must_have) or 1)
        br = int(p.get("bedrooms", 0))
        bedrooms = 1.0 if br >= bedrooms_min else max(0.0, br / max(1, bedrooms_min))
        out[p["id"]] = {"price": price_score, "amenities": amenities, "bedrooms": bedrooms, "sentiment": 0.0}
    return out

def _apply_sentiment(base: Mapping[str, ScoreBreakdown],
                     summaries: Mapping[str, Mapping[str, Any]],
                     prefs: UserPreferences) -> Dict[str, Tuple[float, ScoreBreakdown]]:
    w: PrefWeights = {"price": 0.35, "amenities": 0.25, "bedrooms": 0.15, "sentiment": 0.25}
    w.update(prefs.get("weights", {}))
    final: Dict[str, Tuple[float, ScoreBreakdown]] = {}
    for pid, b in base.items():
        s = float(summaries.get(pid, {}).get("sentiment", 0.5))
        bd = {
            "price": b["price"] * w["price"],
            "amenities": b["amenities"] * w["amenities"],
            "bedrooms": b["bedrooms"] * w["bedrooms"],
            "sentiment": s * w["sentiment"],
        }
        final[pid] = (max(0.0, min(1.0, sum(bd.values()))), bd)
    return final

def _advisor_md(props: List[Property],
                finals: Mapping[str, Tuple[float, ScoreBreakdown]],
                sums: Mapping[str, Mapping[str, Any]],
                prefs: UserPreferences,
                top_k: int = 5) -> str:
    idx = {p["id"]: p for p in props if p.get("id")}
    ranked = sorted([pid for pid in finals.keys() if pid in idx],
                    key=lambda pid: finals[pid][0], reverse=True)[:top_k]
    lines: List[str] = ["# Top Airbnb Recommendations", ""]
    for r, pid in enumerate(ranked, 1):
        prop = idx[pid]; score, bd = finals[pid]
        lines.append(f"## {r}. {prop.get('title','')} — Score: **{score:.2f}**")
        if prop.get("url"): lines.append(f"[Open Listing]({prop['url']})  ")
        lines.append("")
        reasons = []
        pr = prefs.get("price_range", {})
        price = prop.get("price", {}).get("per_night")
        if price is not None and pr and price <= pr.get("max", 0): reasons.append(f"Within budget (~{price} {prop.get('price',{}).get('currency','')})")
        if bd.get("amenities", 0) >= 0.24: reasons.append("Meets must-have amenities")
        if bd.get("bedrooms", 0) >= 0.14: reasons.append("Satisfies bedroom requirement")
        if reasons: lines.append("**Why this place:** " + "; ".join(reasons))
        lines.append("")
        summary = sums.get(pid, {}).get("review_summary", "") or "_No review summary available._"
        lines.append("**Review Highlights:**"); lines.append(summary); lines.append("")
        lines.append("**Score Breakdown:** " + ", ".join(f"{k}: {v:.2f}" for k, v in bd.items()))
        lines.append("")
    return "\n".join(lines)

# ----------------------------
# Nodes
# ----------------------------

@dataclass
class Nodes:
    provider: ListingProvider

    # 1) Search
    async def search(self, state: GraphState) -> Dict[str, Any]:
        raw = await self.provider.search(state["prefs"])
        return {"raw_results": raw}

    # HITL A: after search
    def hitl_after_search(self, state: GraphState) -> Dict[str, Any]:
        if state.get("mode") != "hitl": return {}
        # Ask human to review/modify prefs based on raw_count
        result = interrupt({
            "stage": "after_search",
            "raw_count": len(state.get("raw_results", [])),
            "prefs": state.get("prefs", {}),
            "message": "Review criteria? Return {'prefs': <updated dict>} to modify, or {} to approve."
        })
        # When resumed, 'result' is the user's value
        if isinstance(result, dict) and result.get("prefs"):
            updated = dict(state.get("prefs", {})); updated.update(result["prefs"])
            return {"prefs": updated}
        return {}

    # 2) Coding
    def coding(self, state: GraphState) -> Dict[str, Any]:
        return {"structured": _coding(state.get("raw_results", []))}

    # 3) Filtering
    def filtering(self, state: GraphState) -> Dict[str, Any]:
        props = _dedupe(state.get("structured", []))
        return {"filtered": _filtering(props, state["prefs"])}

    # HITL B: after filtering
    def hitl_after_filtering(self, state: GraphState) -> Dict[str, Any]:
        if state.get("mode") != "hitl": return {}
        result = interrupt({
            "stage": "after_filtering",
            "filtered_count": len(state.get("filtered", [])),
            "prefs": state.get("prefs", {}),
            "message": "Adjust filters? Return {'prefs': <updated dict>} to modify, or {} to approve."
        })
        if isinstance(result, dict) and result.get("prefs"):
            updated = dict(state.get("prefs", {})); updated.update(result["prefs"])
            return {"prefs": updated}
        return {}

    # 4a) Summarize (parallel)
    def summarize(self, state: GraphState) -> Dict[str, Any]:
        return {"summaries": _summarize(state.get("filtered", []))}

    # 4b) Rate base (parallel)
    def rate_base(self, state: GraphState) -> Dict[str, Any]:
        return {"base_scores": _base_scores(state.get("filtered", []), state["prefs"])}

    # 5) Merge + apply sentiment (fan-in)
    def merge(self, state: GraphState) -> Dict[str, Any]:
        if not state.get("summaries") or not state.get("base_scores"):
            # Wait until both are present
            return {}
        return {"final_scores": _apply_sentiment(state["base_scores"], state["summaries"], state["prefs"])}

    # 6) Advisor → Markdown
    def advisor(self, state: GraphState) -> Dict[str, Any]:
        md = _advisor_md(state.get("filtered", []), state.get("final_scores", {}), state.get("summaries", {}), state["prefs"])
        return {"report_md": md}

# ----------------------------
# Graph assembly
# ----------------------------

def build_graph(provider: ListingProvider):
    nodes = Nodes(provider)
    g = StateGraph(GraphState)

    g.add_node("search", nodes.search)
    g.add_node("hitl_after_search", nodes.hitl_after_search)
    g.add_node("coding", nodes.coding)
    g.add_node("filtering", nodes.filtering)
    g.add_node("hitl_after_filtering", nodes.hitl_after_filtering)
    g.add_node("summarize", nodes.summarize)
    g.add_node("rate_base", nodes.rate_base)
    g.add_node("merge", nodes.merge)
    g.add_node("advisor", nodes.advisor)

    # START -> Search
    g.add_edge(START, "search")

    # Conditionally enter HITL after search
    def route_after_search(state: GraphState) -> str:
        return "hitl" if state.get("mode") == "hitl" else "skip"
    g.add_conditional_edges("search", route_after_search, {"hitl": "hitl_after_search", "skip": "coding"})
    g.add_edge("hitl_after_search", "coding")

    # Coding -> Filtering
    g.add_edge("coding", "filtering")

    # Conditionally enter HITL after filtering; else fan-out
    def route_after_filtering(state: GraphState) -> str:
        return "hitl" if state.get("mode") == "hitl" else "fanout"
    g.add_conditional_edges("filtering", route_after_filtering,
                            {"hitl": "hitl_after_filtering", "fanout": "summarize"})
    # If we went through HITL, proceed to fan-out
    g.add_edge("hitl_after_filtering", "summarize")
    g.add_edge("hitl_after_filtering", "rate_base")
    # When we skip HITL, we also need to kick off rate_base
    g.add_edge("filtering", "rate_base")

    # Fan-in merge: only advance to advisor when both are ready
    def merge_gate(state: GraphState) -> str:
        return "go" if state.get("final_scores") else "wait"
    g.add_edge("summarize", "merge")
    g.add_edge("rate_base", "merge")
    g.add_conditional_edges("merge", merge_gate, {"go": "advisor", "wait": "merge"})

    # Advisor -> END
    g.add_edge("advisor", END)

    # Compile with a checkpointer to enable interrupts/persistence
    app = g.compile(checkpointer=MemorySaver())
    return app

# ----------------------------
# Demo data and run helpers
# ----------------------------

EXAMPLE_ITEMS = [
    {
        "id": "LIS-1001",
        "title": "Sunny Alfama 2BR with Balcony",
        "url": "https://example.com/listings/LIS-1001",
        "location": {"city": "Lisbon", "country": "Portugal"},
        "price": {"per_night": 145.0, "currency": "EUR"},
        "bedrooms": 2, "bathrooms": 1.0, "accommodates": 4,
        "amenities": ["WiFi", "Kitchen", "Washer", "Balcony", "Air conditioning"],
        "reviews": [{"rating": 4.8, "date": "2025-05-12", "text": "Clean, walkable area, friendly host.", "reviewer_name": "M"}],
    },
    {
        "id": "LIS-1002",
        "title": "Central Baixa Loft",
        "url": "https://example.com/listings/LIS-1002",
        "location": {"city": "Lisbon", "country": "Portugal"},
        "price": {"per_night": 110.0, "currency": "EUR"},
        "bedrooms": 1, "bathrooms": 1.0, "accommodates": 2,
        "amenities": ["WiFi", "Kitchen", "Air conditioning"],
        "reviews": [{"rating": 4.2, "date": "2025-06-01", "text": "Great location but noisy street.", "reviewer_name": "A"}],
    },
]

def demo_autonomous():
    provider = ListingProvider(EXAMPLE_ITEMS)
    app = build_graph(provider)
    prefs: UserPreferences = {
        "location": "Lisbon, Portugal",
        "date_range": {"checkin": "2025-09-10", "checkout": "2025-09-15"},
        "guests": 2, "bedrooms_min": 1,
        "price_range": {"min": 80.0, "max": 160.0},
        "amenities": {"must_have": ["WiFi", "Kitchen"], "nice_to_have": ["Washer", "Balcony"]},
        "weights": {"price": 0.35, "amenities": 0.25, "bedrooms": 0.15, "sentiment": 0.25},
        "sort_by": "score",
    }
    out = app.invoke({"mode": "autonomous", "prefs": prefs},
                     config={"configurable": {"thread_id": "airbnb-demo-autonomous"}})
    return out["report_md"]

def demo_hitl_roundtrip(user_update_after_search: Dict[str, Any] | None = None,
                        user_update_after_filter: Dict[str, Any] | None = None):
    """
    Example of driving interrupts from code. In practice, your UI would inspect the interrupt payloads
    and gather human input, then resume with Command(resume=...).
    """
    provider = ListingProvider(EXAMPLE_ITEMS)
    app = build_graph(provider)
    prefs: UserPreferences = {
        "location": "Lisbon, Portugal",
        "date_range": {"checkin": "2025-09-10", "checkout": "2025-09-15"},
        "guests": 2, "bedrooms_min": 1,
        "price_range": {"min": 80.0, "max": 160.0},
        "amenities": {"must_have": ["WiFi", "Kitchen"], "nice_to_have": ["Washer", "Balcony"]},
        "weights": {"price": 0.35, "amenities": 0.25, "bedrooms": 0.15, "sentiment": 0.25},
        "sort_by": "score",
    }
    cfg = {"configurable": {"thread_id": "airbnb-demo-hitl"}}

    # Kick off
    for event in app.stream({"mode": "hitl", "prefs": prefs}, cfg, stream_mode="values"):
        if "__interrupt__" in event:
            # FIRST interrupt: after_search
            payload = event["__interrupt__"][0].value  # dict from interrupt(...)
            # Simulate human approving or tweaking prefs
            resume_value = user_update_after_search or {}
            for nxt in app.stream(Command(resume=resume_value), cfg, stream_mode="values"):
                if "__interrupt__" in nxt:
                    # SECOND interrupt: after_filtering
                    resume_value_2 = user_update_after_filter or {}
                    final = app.invoke(Command(resume=resume_value_2), cfg)
                    return final["report_md"]
            # If only one interrupt occurred, we would return after second stage completes
            break
    # Fallback (shouldn't hit in normal flow)
    return app.invoke(Command(resume={}), cfg)["report_md"]
```

> Validation: Nodes define each stage; conditional edges handle HITL vs skip; merge gate ensures advisor runs only when both branches are ready; graph compiles with a checkpointer (required for interrupts); proceed.

---

### 4) How to use (quick)
- **Autonomous mode (no pauses)**:

```python
print(demo_autonomous())
```

- **HITL mode**: the graph interrupts twice with payloads like `{"stage": "after_search", ...}`. Your UI inspects these payloads and resumes by sending `Command(resume=<your dict>)` back to the graph (e.g., `{"prefs": {...updated...}}`). The demo uses `app.stream(...)` to catch interrupts and `Command(resume=...)` to continue.

---

### Notes & knobs
- **Checkpointer choices**: use `MemorySaver`/`InMemorySaver` for development; for durability, swap to SQLite/Postgres checkpointers.
- **Parallelism**: achieved by connecting Filtering to both `summarize` and `rate_base` and then fanning in at `merge`.
- **Conditional routing**: `add_conditional_edges` routes based on `state["mode"]`.
- **HITL mechanics**: `interrupt(...)` pauses the node and surfaces the provided value; resume with `Command(resume=...)`. Requires a checkpointer.

￼

