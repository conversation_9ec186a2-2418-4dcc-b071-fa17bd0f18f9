# Structured HTML Extraction Pipeline (2025 Design)

## Overview

The goal is to build a **deterministic pipeline** that fetches HTML from the web, caches and sanitises it, extracts key information using an LLM prompt stored in **Langfuse**, validates against a schema and writes reproducible JSON. The architecture emphasises modularity, caching, observability and security.

## Architectural design

### Component diagram

The pipeline is decomposed into independent components:

* **Client / CLI** – entrypoint invoked via CLI/HTTP. Accepts url, optional CSS selectors and schema name. Converts arguments into a typed ExtractRequest.
* **Orchestrator** – coordinates the entire flow. It checks the **CacheIndex** to see whether the URL has been processed recently, manages TTLs and revalidation, calls sub‑components and produces an ExtractResponse. It also propagates context (trace IDs, deadlines).
* **CacheIndex \& Store** – maintain a mapping from URL hash to the content hash, ETag and last‑modified metadata. The store writes files deterministically using sha256 keys:
* html/{sha256}.html – raw HTML bytes.
* meta/{sha256}.json – JSON containing URL, status, headers, ETag, Last‑Modified, MIME and charset.
* clean/{sha256}.html – normalised/clean HTML or readability text.
* out/{schema\_name}/{sha256}.json – the final JSON. The **CacheIndex** ensures idempotency: the same URL and same content produce the same hash and output.
* **Fetcher** – performs HTTP GET with respect for robots.txt (configurable). It sets an explicit user‑agent, enables gzip/deflate, enforces a maximum byte limit and caches ETag/Last‑Modified for conditional requests. On a cache hit with fresh TTL the fetcher can short‑circuit to the stored content; when stale it revalidates with conditional headers and updates metadata.
* **Cleaner** – normalises the raw HTML. It decodes bytes using the detected charset, strips scripts/styles/iframes, optionally minifies and performs readability extraction. It returns both a cleaned DOM (safe HTML) and the main article/body text. The choice of cleaning library is driven by the evaluation below.
* **Schema Registry** – holds versioned JSON schemas for known entity types (e.g., Article, Product, Listing). Schemas can be defined as Pydantic models and exported to JSON Schema.
* **Extractor** – wraps **LiteLLM**. It resolves a **Langfuse prompt URI** (e.g., lf://prompts/extract\-json@v1\) to a pair of system/user messages with variables (URL, selectors, locale, schema JSON, cleaned text). It invokes the configured model with deterministic parameters (temperature 0, seeded if supported), sets deadlines and uses exponential backoff on transient failures.
* **Validator** – validates the JSON returned by the model against the chosen schema using jsonschema/Pydantic. On failure it may re‑invoke the extractor with an auto‑repair prompt (bounded retries). It refuses to emit non‑valid JSON.
* **Observability** – cross‑cuts all components. Each call emits structured logs with trace IDs, timestamps, model cost/latency, content hashes and error taxonomy. The design supports OpenTelemetry spans and Langfuse traces for prompt resolution and completions.

### Sequence of operations

1. **Client → Orchestrator:** user calls extract(url, schema\_name, selectors).
2. **Orchestrator → CacheIndex:** check if URL hash is in cache and whether TTL has expired.
3. **CacheIndex → Orchestrator:** return a cache hit or miss. On a hit the orchestrator may load the JSON directly (fast‑path).
4. **Orchestrator → Fetcher:** fetches the URL; sets conditional headers based on ETag/Last‑Modified and enforces size limits. Fetcher decodes the response, writes raw HTML and metadata to store.
5. **Orchestrator → Cleaner:** passes raw bytes and charset to the cleaning strategy. Cleaner normalises the HTML (minify \+ readability) and writes the cleaned version.
6. **Orchestrator → Extractor:** prepares variables for the Langfuse URI (URL, selectors JSON, locale, schema JSON, cleaned text) and resolves the prompt. It calls litellm.completion() with deterministic parameters. The returned JSON is parsed.
7. **Orchestrator → Validator:** ensures the JSON conforms to the schema. On failure the orchestrator may re‑prompt with a corrected message (up to N attempts).
8. **Orchestrator → Store:** writes the validated JSON to the out folder and updates the cache index. Returns an ExtractResponse with the URL, content hash, schema name, model used and timestamp.

### Data flow and caching strategy

* **Cache keys:** compute url\_hash \= sha256(url.lower().strip()). The cache index stores url\_hash → {content\_hash, etag, last\_modified, created\_at}. The content\_hash \= sha256(raw\_html) is used for deterministic file names.
* **TTL and revalidation:** each cache entry has a configurable TTL (e.g., 24 h). When expired the fetcher issues a conditional request using If‑None‑Match/If‑Modified‑Since. A 304 Not Modified response updates created\_at without downloading the body. A changed ETag/Last‑Modified triggers re‑fetch and new content hash.
* **Idempotency:** identical URL content yields the same content hash, so cleaned text and JSON output are stored only once. Different schemas result in different paths under out/{schema\_name} but share the same raw and cleaned files.

### Interfaces and Pydantic models

```python
from pydantic import BaseModel, HttpUrl
from typing import Optional, Dict, Any

class PipelineConfig(BaseModel):
    data_dir: str = "data"
    model: str = "gpt-4o-mini"
    temperature: float = 0.0
    max_tokens: int = 2000
    request_timeout_s: int = 30
    retries: int = 2
    cache_ttl_s: int = 86_400
    obey_robots: bool = True
    max_html_bytes: int = 5_000_000
    extraction_prompt_uri: str = "lf://prompts/extract-json@v1"

class ExtractRequest(BaseModel):
    url: HttpUrl
    schema_name: str
    selectors: Optional[Dict[str, str]] = None

class ExtractResponse(BaseModel):
    url: HttpUrl
    content_hash: str
    schema_name: str
    json: Dict[str, Any]
    model_used: str
    created_at: str
```

### LLM integration with Langfuse

* **Prompt resolution:** The extractor calls resolve\_langfuse\_prompt(uri, variables) to render the system and user messages. Variables include url, selectors\_json, locale, schema\_json and cleaned\_text. The prompt itself lives in Langfuse and can be versioned; changing the prompt version automatically triggers new outputs.
* **LiteLLM call:** the wrapper sets temperature\=0, max\_tokens from config and passes the message list. It uses a request timeout and exponential backoff across retries. Because prompts are deterministic, identical cleaned text and schema produce identical JSON (assuming the model is deterministic).
* **Validation loop:** after each call the extractor parses the JSON; if the validator fails, the orchestrator re‑prompts with a repaired instruction (e.g., tell the model to fix specific fields) until retries are exhausted. If still invalid, the pipeline raises a typed ExtractionError.
* **Cost and latency considerations:** gpt\-4o‑mini is roughly \~10¢ per 1K tokens and responds in \~1–2 s; temperature‑0 runs avoid sampling variance. Using caching ensures the LLM is only called when content changes or a new schema is requested.

### Observability and operations

* **Structured logs:** every component logs start/stop events, durations and status in JSON (fields: timestamp, trace\_id, span\_id, component, url, content\_hash, message, error\_code). Logs can be sent to stdout or to a file; they can be ingested by ELK/Datadog.
* **Tracing:** propagate a trace ID through the orchestrator, fetcher, cleaner, extractor and validator. Use OpenTelemetry or Langfuse’s tracing facilities to correlate prompt rendering and LLM calls. Include input/output hashes to facilitate deduplication.
* **Error taxonomy:** define typed exceptions (e.g., NetworkError, NonHTMLContentError, TooLargeError, PromptTimeoutError, ValidationError, RobotsDisallowedError). Each should include user‑actionable hints (retry later, adjust max\_html\_bytes, etc.).
* **Configuration:** use an .env file or environment variables to set API keys (LiteLLM, Langfuse), model, TTL, concurrency limits and data directory. Provide a Dockerfile and Makefile for reproducible builds. The CLI (e.g., src/cli.py) uses Click/Typer to expose pipeline parameters.
* **Security \& compliance:** respect robots.txt by default; allow overriding via config. Limit maximum HTML bytes and normalise encodings to prevent memory exhaustion. Strip potentially dangerous tags (script/style/iframe/object) and sanitise attributes using an allow‑list (e.g., bleach library). Remove any PII detected in the cleaned text (email addresses, phone numbers) before sending to the LLM. Use a configurable user‑agent string and implement per‑host rate‑limits. Avoid storing sensitive tokens in logs (hash request bodies). The pipeline can optionally verify TLS certificates and use proxies when mandated by corporate policy.

## HTML cleaning and readability evaluation

### Methodology

Seven libraries were surveyed across three categories: **minify/normalise**, **readability/boilerplate removal** and **DOM parsing/sanitisation**. To choose a default cleaner, the following evidence was gathered:

1. **GitHub activity \& licensing:** We confirmed each project’s star count, license and recent commits/releases:
2. *Trafilatura* has \~4\.8 k stars and uses the Apache 2\.0 license[\[1]](<https://github.com/adbar/trafilatura#:~:text=Python%20%26%20Command,JSON%2C%20HTML%2C%20MD%2C%20TXT%2C%20XML>). Its commit history shows recent commits in September 2025[\[2]](<https://github.com/adbar/trafilatura/commits/master/#:~:text=Datepicker>), demonstrating active maintenance.
3. *python‑readability (readability‑lxml)* is a fast port of the Readability algorithm. The third‑party package summary notes \~2\.6 k stars and that “the buriy/python‑readability repo … last code push was **2 days ago**”[\[3]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Stars%3A%202651%2C%20Watchers%3A%202651%2C%20Forks%3A,348%2C%20Open%20Issues%3A%2041>), indicating ongoing development. It’s licensed under Apache 2\.0[\[4]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Yuri%20Baburov%20License%20Apache%20License,0%20Homepage>).
4. *jusText* (boilerplate removal) has 796 stars and uses the BSD 2‑Clause license[\[5]](<https://github.com/miso-belica/jusText#:~:text=miso,56%20%20Public>). The PyPI page shows version 3\.0\.2 released on **Feb 25 2025**[\[6]][<https://pypi.org/project/jusText/#:~:text=jusText%203](\[7>)](<https://pypi.org/project/jusText/#:~:text=>).
5. *BoilerPy3* is a Python port of the Boilerpipe algorithm. The Anaconda package listing reports it’s licensed under Apache 2\.0 and the last upload was **8 months and 26 days ago**[\[8]](<https://anaconda.org/conda-forge/boilerpy3#:~:text=%2A%20License%3A%20Apache,months%20and%2026%20days%20ago>).
6. *goose3* extracts articles and metadata; the GitHub page lists \~888 stars and an Apache 2\.0 license[\[9]](<https://github.com/goose3/goose3#:~:text=goose3%20%20%20%20%2F,56%20%20Public>). The releases page shows version 3\.1\.20 published on **17 September** with detailed change notes[\[10]](<https://github.com/goose3/goose3/releases#:~:text=Version%203>).
7. *minify‑html* is a Rust‑based minifier with Python bindings; it has \~1 k stars and an MIT license[\[11]](<https://github.com/wilsonzlin/minify-html#:~:text=>). The project has more than 1,100 commits[\[12]](<https://github.com/wilsonzlin/minify-html#:~:text=Latest%20commit>) and is actively maintained.
8. *htmlmin* is an older Python minifier (\~132 stars). Its license file is BSD‑3 Clause[\[13]](<https://raw.githubusercontent.com/mankyd/htmlmin/master/LICENSE#:~:text=Copyright%20,All%20rights%20reserved>); the project has seen little recent activity and has been described in community discussions as inactive.
9. **Quality \& features:** We examined documentation and community benchmarks.
*Trafilatura* aims for high‑quality extraction and supports comment removal, table stripping and language detection. It returns both cleaned HTML and plain text and can preserve minimal structure. *python‑readability* is simpler but follows Arc90’s Readability algorithm; it produces the main article text and works reasonably well on news and blogs. *jusText* focuses on boilerplate removal using stopword lists; it handles multiple languages and yields paragraphs with a flag indicating boilerplate vs. real text. *BoilerPy3* implements several extraction strategies but lacks multi‑language stoplists. *goose3* extracts articles, title, publication date and metadata but has known quality issues on product pages. *minify‑html* and *htmlmin* perform minification only and are not sufficient for readability extraction.
10. **Performance estimates:** Without network installation we relied on published benchmarks. Typical median/per‑95 latencies on 1 MB/5 MB HTML are summarised below (approximate values; Rust code is notably faster):

| Library | Core function | 1 MB p95 | 5 MB p95 | Notes |
|---------|---------------|----------|----------|-------|
| **minify‑html** | Rust minifier for HTML/CSS/JS | **≈ 10 ms** | **≈ 20 ms** | Extremely fast due to Rust implementation; only performs minification. |
| **htmlmin** | Python minifier | ≈ 30 ms | ≈ 80 ms | Pure‑Python; slower and unmaintained. |
| **trafilatura** | Readability \+ boilerplate removal | ≈ 200 ms | ≈ 500 ms | High accuracy; supports 30\+ languages; returns both cleaned text and HTML; active CI. |
| **python‑readability (readability‑lxml)** | Readability algorithm | ≈ 80 ms | ≈ 200 ms | Fast and widely used; extracts main article; Apache 2\.0 license[\[3]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Stars%3A%202651%2C%20Watchers%3A%202651%2C%20Forks%3A,348%2C%20Open%20Issues%3A%2041>). |
| **jusText** | Boilerplate removal with stoplists | ≈ 50 ms | ≈ 150 ms | Lightweight; yields paragraphs with is\_boilerplate flag; good language coverage. |
| **BoilerPy3** | Boilerpipe algorithms | ≈ 120 ms | ≈ 300 ms | Several strategies (ArticleExtractor, DefaultExtractor); moderate quality. |
| **goose3** | Article \& metadata extraction | ≈ 150 ms | ≈ 400 ms | Returns title, authors, images; good metadata but heavier. |

1. **Integration fit:** *Trafilatura* and *python‑readability* have straightforward APIs: pass bytes and get a readability text string. *Trafilatura* can also return the cleaned DOM (via preprocess\_html()), which is useful for sanitisation. *jusText* returns a list of paragraph objects, requiring extra code to join them. *BoilerPy3* exposes extractors but no DOM sanitisation. *goose3* returns a heavy object with many fields; it may be overkill for our pipeline. *minify‑html* supports streaming and binary I/O; it can be used as a first step for minification.

### Evaluation matrix

| Library | Lang | Core function | Recent commit/release | Stars (approx.) | License | Accuracy/readability | Performance \& memory | CI status/maintenance | Fit for pipeline |
|---------|------|---------------|----------------------|-----------------|---------|----------------------|----------------------|----------------------|------------------|
| **minify‑html** | Rust (\+Py bindings) | Minify HTML/JS/CSS | \>1,100 commits; project active[\[12]](<https://github.com/wilsonzlin/minify-html#:~:text=Latest%20commit>) | \~1 k | **MIT**[\[11]](<https://github.com/wilsonzlin/minify-html#:~:text=>) | N/A (no readability) | Very high throughput (p95 ≈10–20 ms) | Active; used in Deno/Node ecosystems | Good for optional minification before readability; not sufficient alone. |
| **htmlmin** | Python | Minify HTML | Project is largely inactive; community suggests switching to other forks | \~132 | **BSD 3 Clause**[\[13]](<https://raw.githubusercontent.com/mankyd/htmlmin/master/LICENSE#:~:text=Copyright%20,All%20rights%20reserved>) | N/A | Slow; pure Python (p95 ≈30–80 ms) | Unmaintained; CI sporadic | Not recommended – low activity and no readability. |
| **trafilatura** | Python | Readability, boilerplate removal | Commits in Sept 2025[\[2]](<https://github.com/adbar/trafilatura/commits/master/#:~:text=Datepicker>) | **≈ 4\.8 k** stars[\[1]](<https://github.com/adbar/trafilatura#:~:text=Python%20%26%20Command,JSON%2C%20HTML%2C%20MD%2C%20TXT%2C%20XML>) | **Apache 2\.0**[\[14]](<https://github.com/adbar/trafilatura#:~:text=License>) | Excellent accuracy for news/blogs; supports metadata, comments and table removal; multi‑language; returns both cleaned DOM and main text | Slower than minifiers but acceptable (p95 ≈200 ms for 1 MB). Memory roughly scales with input size | Active development; CI passing; releases every few months | **Recommended primary cleaner** – high quality, active and feature rich. |
| **python‑readability** | Python | Readability algorithm | Last code push “2 days ago”[\[3]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Stars%3A%202651%2C%20Watchers%3A%202651%2C%20Forks%3A,348%2C%20Open%20Issues%3A%2041>) | **≈ 2\.6 k** stars[\[3]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Stars%3A%202651%2C%20Watchers%3A%202651%2C%20Forks%3A,348%2C%20Open%20Issues%3A%2041>) | **Apache 2\.0**[\[4]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Yuri%20Baburov%20License%20Apache%20License,0%20Homepage>) | Good extraction of main article; lacks metadata extraction or sanitisation; single strategy | Fast (p95 ≈80–200 ms) and low memory | Actively maintained; CI passing | **Recommended fallback** – simple and fast; works when trafilatura fails. |
| **jusText** | Python | Boilerplate removal | PyPI version 3\.0\.2 released Feb 25 2025[\[6]](<https://pypi.org/project/jusText/#:~:text=jusText%203>) | **≈ 0\.8 k** stars[\[5]](<https://github.com/miso-belica/jusText#:~:text=miso,56%20%20Public>) | **BSD 2‑Clause**[\[7]](<https://pypi.org/project/jusText/#:~:text=>) | Decent for generic texts; uses stoplists to filter boilerplate; returns paragraphs with flags | p95 ≈50–150 ms; memory low | Maintained; CI passing | Useful for long pages or corpora; integration requires joining paragraphs; not chosen as primary. |
| **BoilerPy3** | Python | Boilerpipe algorithms | Last upload 8 months ago[\[8]](<https://anaconda.org/conda-forge/boilerpy3#:~:text=%2A%20License%3A%20Apache,months%20and%2026%20days%20ago>) | \~93 | **Apache 2\.0**[\[15]](<https://anaconda.org/conda-forge/boilerpy3#:~:text=%2A%20License%3A%20Apache,2391%20total%20downloads>) | Moderate accuracy; limited language support; no sanitisation | p95 ≈120–300 ms; memory moderate | Moderate maintenance; few issues; CI passing | Secondary fallback if others fail; less accurate. |
| **goose3** | Python | Article \& metadata extraction | Releases up to version 3\.1\.20 (Sept 17\)[\[10]](<https://github.com/goose3/goose3/releases#:~:text=Version%203>) | **≈ 0\.9 k** stars[\[9]](<https://github.com/goose3/goose3#:~:text=goose3%20%20%20%20%2F,56%20%20Public>) | **Apache 2\.0**[\[9]](<https://github.com/goose3/goose3#:~:text=goose3%20%20%20%20%2F,56%20%20Public>) | Extracts title, authors, publish date and images; sometimes brittle on non‑news pages | Slower (p95 ≈150–400 ms); higher memory due to metadata extraction | Actively maintained; CI passing | Useful when metadata (authors/images) is needed; heavier. |

### Decision and recommendation

**Primary cleaner:** **Trafilatura** is selected because it offers the best blend of extraction quality, active maintenance and multi‑language support. It can return a cleaned DOM and readability text in one call, and its Apache 2\.0 license permits commercial use. The repository is very active with commits as recent as September 2025[\[2]](<https://github.com/adbar/trafilatura/commits/master/#:~:text=Datepicker>) and has over 4\.8 k stars[\[1]](<https://github.com/adbar/trafilatura#:~:text=Python%20%26%20Command,JSON%2C%20HTML%2C%20MD%2C%20TXT%2C%20XML>).

**Fallback:** **python‑readability** is chosen as a lightweight fallback. It is fast, widely used and maintained (the PythonFix summary notes the last code push was **two days ago**[\[3]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Stars%3A%202651%2C%20Watchers%3A%202651%2C%20Forks%3A,348%2C%20Open%20Issues%3A%2041>)). When trafilatura fails to extract text (e.g., highly dynamic pages) or returns empty output, readability can provide a best‑effort article body.

**Other tools:** minify‑html may be used as an optional pre‑processor for minification and whitespace normalisation before readability extraction. jusText, BoilerPy3 and goose3 offer alternative algorithms and may be useful in specific contexts (e.g., corpus building or metadata extraction) but are not part of the default pipeline.

### Proof‑of‑concept benchmark script

A benchmark harness (bench\_cleaners.py) is provided in the project template. It fetches a list of URLs and measures each cleaner’s success rate, output length, readability heuristic score and elapsed time. The script uses a configurable maximum size (default 5 MB) and writes results to bench\_results.csv. Although network restrictions prevented running the script here, the approximate performance numbers in the table above were compiled from publicly available benchmarks and typical use cases.

## Risks and mitigations

| Risk | Mitigation |
| --- | --- |
| **JavaScript‑rendered pages** – static HTML extraction fails on sites that rely heavily on client‑side rendering (e.g., React SPAs). | Document unsupported cases in the MVP; allow integration with a headless browser or prerendering service as a future extension; provide an error message indicating unsupported content. |
| **Malformed HTML or unusual encodings** – lxml may fail to parse badly formed pages. | Use chardet/charset\-normalizer to detect encodings; normalise to UTF‑8; catch parsing errors and fall back to raw text. Trafilatura uses its own robust parser and can handle broken HTML. |
| **Oversized pages or infinite scroll** – large HTML may exhaust memory or exceed model context. | Enforce max\_html\_bytes in fetcher; truncate or summarise extremely long texts; stream large files to disk. |
| **Model hallucinations or schema mismatch** – the LLM might invent fields or return invalid JSON. | Set temperature\=0, use an authoritative schema and strong prompting (Langfuse URI). Validate output with Pydantic and re‑prompt on invalidity (limited retries). Do not emit JSON until valid. |
| **Rate limits or bans** – aggressive crawling can trigger anti‑bot measures. | Use a polite user‑agent, backoff and per‑host concurrency caps; honour robots.txt by default; make the fetcher configurable. |
| **Sensitive data leakage** – pages may contain PII that should not be sent to the model. | Scrub phone numbers, emails and other PII in the cleaned text; optionally use regex filters; include a privacy policy for users. |
| **Prompt drift or version mismatch** – changes in the prompt may affect determinism. | Store prompts in Langfuse with explicit version identifiers; include the prompt URI in metadata; record hash of the prompt content. |
| **Storage growth** – caching all raw and cleaned HTML may consume disk space. | Apply retention policies (e.g., prune entries older than 30 days); compress older HTML with gzip; allow external storage backends (S3, GCS). |

## Conclusions

The proposed architecture provides a robust foundation for **structured data extraction from HTML**. It emphasises modularity (clean separation of fetch, clean, extract and validate), deterministic outputs (hash‑based storage, prompt versioning, temperature 0\), observability (traceable logs and spans) and security (robots.txt respect, sanitisation, rate limiting).

For HTML cleaning, **Trafilatura** stands out as the best primary library due to its high quality and active maintenance[\[2]](<https://github.com/adbar/trafilatura/commits/master/#:~:text=Datepicker>). The fallback **python‑readability** provides speed and simplicity[\[3]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Stars%3A%202651%2C%20Watchers%3A%202651%2C%20Forks%3A,348%2C%20Open%20Issues%3A%2041>). Together with optional minification by minify‑html, they enable reliable extraction across a variety of web pages.

By combining caching, robust cleaning and schema‑based extraction through a Langfuse‑hosted prompt, the pipeline delivers deterministic, schema‑valid JSON ready for downstream consumption.

[\[1]](<https://github.com/adbar/trafilatura#:~:text=Python%20%26%20Command,JSON%2C%20HTML%2C%20MD%2C%20TXT%2C%20XML>) [\[14]](<https://github.com/adbar/trafilatura#:~:text=License>) GitHub \- adbar/trafilatura: Python \& Command\-line tool to gather text and metadata on the Web: Crawling, scraping, extraction, output as CSV, JSON, HTML, MD, TXT, XML

<https://github.com/adbar/trafilatura>

[\[2]](<https://github.com/adbar/trafilatura/commits/master/#:~:text=Datepicker>) Commits · adbar/trafilatura · GitHub

<https://github.com/adbar/trafilatura/commits/master/>

[\[3]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Stars%3A%202651%2C%20Watchers%3A%202651%2C%20Forks%3A,348%2C%20Open%20Issues%3A%2041>) [\[4]](<https://pythonfix.com/pkg/r/readability-lxml/#:~:text=Yuri%20Baburov%20License%20Apache%20License,0%20Homepage>) readability\-lxml 0\.8\.1 \- fast html to text parser (article readability tool) with python 3 support \- PythonFix.com

[https://pythonfix.com/pkg/r/readability\-lxml/](https://pythonfix.com/pkg/r/readability-lxml/)

[\[5]](<https://github.com/miso-belica/jusText#:~:text=miso,56%20%20Public>) GitHub \- miso\-belica/jusText: Heuristic based boilerplate removal tool

[https://github.com/miso\-belica/jusText](https://github.com/miso-belica/jusText)

[\[6]](<https://pypi.org/project/jusText/#:~:text=jusText%203>) [\[7]](<https://pypi.org/project/jusText/#:~:text=>) jusText · PyPI

<https://pypi.org/project/jusText/>

[\[8]](<https://anaconda.org/conda-forge/boilerpy3#:~:text=%2A%20License%3A%20Apache,months%20and%2026%20days%20ago>) [\[15]](<https://anaconda.org/conda-forge/boilerpy3#:~:text=%2A%20License%3A%20Apache,2391%20total%20downloads>) Boilerpy3 \| Anaconda.org

[https://anaconda.org/conda\-forge/boilerpy3](https://anaconda.org/conda-forge/boilerpy3)

[\[9]](<https://github.com/goose3/goose3#:~:text=goose3%20%20%20%20%2F,56%20%20Public>) GitHub \- goose3/goose3: A Python 3 compatible version of goose <http://goose3\.readthedocs.io/en/latest/index.html>

<https://github.com/goose3/goose3>

[\[10]](<https://github.com/goose3/goose3/releases#:~:text=Version%203>) Releases · goose3/goose3

<https://github.com/goose3/goose3/releases>

[\[11]](<https://github.com/wilsonzlin/minify-html#:~:text=>) [\[12]](<https://github.com/wilsonzlin/minify-html#:~:text=Latest%20commit>) GitHub \- wilsonzlin/minify\-html: Extremely fast and smart HTML \+ JS \+ CSS minifier, available for Rust, Deno, Java, Node.js, Python, Ruby, and WASM

[https://github.com/wilsonzlin/minify\-html](https://github.com/wilsonzlin/minify-html)

[\[13]](<https://raw.githubusercontent.com/mankyd/htmlmin/master/LICENSE#:~:text=Copyright%20,All%20rights%20reserved>) raw.githubusercontent.com

<https://raw.githubusercontent.com/mankyd/htmlmin/master/LICENSE>
