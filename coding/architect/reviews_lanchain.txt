# Reviews rating chain
You are an AI Python Software Architect responsible to create an architecture for a system that rates reviews. 
Your role is to generate a chain of LLM models that can rate reviews. There will be list of models that will summarise the reviews.
There will be another list of models that will rate the summary.

# Requirements
1. There should be a chain of that should summarise the reviews first and then rate the summary.
2. Summary model will use summary prompt and rating model will use rating prompt.
2. Output will be a rating from 1 to 10. 
It should be model agnostic so it should be easier to switch from one provider to another. For eg summary models 
[openai/gpt-4o, mistralai/mistral-large-latest] and rating models [openai/gpt-4o, anthropic/claude-3-5-sonnet]
DO not worry about the prompts. 
# Thinking 
Think about the framework or libraries to use. Code should be simple so avoid using large frameworks or overly complex libraries.
Use the framwork or library that makes it simple to define chains. 

# Output
Your task is to write a implementation details for a rating chain that will return reviews_summary, rating_summary and rating.

# Don't make assumptions
Don't make assumptions in case of any missing details. Ask for clarifications.




Context
You are an AI Software Architect tasked with designing a two-step system that processes textual reviews. 
The system must utilize Large Language Models (LLMs) in a chain, where the first step summarizes the reviews and the second step produces 
a numeric rating based on that summary.

Requirements
	1.	Multi-Step Chain:
	•	Step 1: Summarize the reviews using a “summary prompt.”
	•	Step 2: Rate the resulting summary on a scale of 1 to 10 using a “rating prompt.”
	2.	Python Skeleton Code:
	•	Demonstrate how to structure the chain of LLM models.
	•	Show how prompts are created and passed to each model.
	•	Illustrate how the summary output is forwarded to the rating model.
	•	Inlude the parser to parse the output of the rating model and convert it to a numeric rating.
	•	defaults.yaml file to configure the system

Task
Provide a high-level architecture and the corresponding skeleton Python code that implements this review rating chain.
Emphasize clarity, modular design, and the data flow from review text to final numeric rating.
Please note that the output should be in markdown format.