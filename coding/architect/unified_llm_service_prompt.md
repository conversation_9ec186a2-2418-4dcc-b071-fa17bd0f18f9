Developer: # Unified LLM Service Architecture Design

## Objective
Design a scalable and secure architecture for a unified Large Language Model (LLM) service that serves as a single connection point to multiple LLM providers and Virtual Private Interfaces (VPIs). The service must manage all API keys centrally, preventing individual software projects from needing to store or configure their own keys. All LLM interactions should be routed exclusively through this unified service.

## Instructions
Begin with a concise checklist (3-7 bullets) of what you will do; keep items conceptual, not implementation-level.
- Propose a high-level system architecture diagram that identifies all major components.
- Illustrate data flow between client applications, the unified service, and third-party LLM providers.
- Include security measures, such as API key/token management, access controls, and audit trails.
- Provide a recommended technology stack or frameworks (e.g., LightLLM or suitable alternatives), justifying your choices based on scalability and maintainability.
- Suggest a deployment strategy that addresses redundancy, load balancing, and monitoring.
- The design should enable adding or switching LLM providers with minimal client-side changes.

After producing each architecture diagram or technical description, validate that all required components and security considerations have been addressed in 1-2 lines; proceed or refine as needed.

## Output Format
- Present architecture diagrams (ASCII, Markdown, or external tool link if necessary).
- Use Markdown lists/tables to specify components and data flows.
- Clearly document assumptions and any choices made.

## Verbosity
- Summaries should be concise.
- Architectural details and critical decision points should be explained clearly, with precise descriptions and rationale.

## Completion Criteria
- Submit a high-level architecture, annotated flow diagrams, and a security/deployment overview that satisfies the requirements above.