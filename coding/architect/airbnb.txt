Please review and refine the following prompt. You are an AI software architect or solution architect working on an 
Airbnb search and recommendation app. Performing search and recommending a small set of accommodation involves multiple steps.
Steps involved in the process include performing the search using an Airbnb scraper, filtering the results based on a set of criteria.
After the results are filtered, the reviews are requested and scraped for the filtered results. 
Then a review summary is created for each of the accommodation in the filtered list and then LLM model is used to create a summary of 
all the reviews which is then passed to another LLM model to create a rating out of 1 to 10. And then all the results are displayed in a 
UI with LLM model rating included. User can then browse through the results and apply more criteria to finally come up with a shortlist 
of accommodation. Please create a architecture to develop such solution. Identify the most suitable libraries and framework for the task. 
Please make sure that solution, or an architecture should be pretty well integrated and easy to debug. There can be multiple LLM models summarizing 
the reviews and there can also be multiple LLM models creating a rating from the summary. Please try to avoid using the overly complicated libraries and framework.

# Refined Prompt
Airbnb Search and Recommendation Architecture

You are an AI Software Architect or Solution Architect designing an Airbnb search and recommendation app. This system involves multiple steps to perform searches and recommend a small set of accommodations effectively.

Process Overview
1.	Search & Scraping:
	•	Perform an Airbnb search using a web scraper to extract accommodation data.
2.	Filtering:
	•	Apply a set of predefined criteria to filter the search results.
3.	Review Scraping & Summarization:
	•	Scrape reviews for the filtered accommodations.
	•	Generate an individual review summary for each accommodation using an LLM model.
4.	Rating Generation:
	•	Use another LLM model to analyze all reviews and assign a rating (1–10) per accommodation.
	•	Optionally, allow multiple LLM models for both summarization and rating generation.
5.	User Interface (UI) Display:
	•	Present the final list of accommodations in a UI with:
	•	Filtered results
	•	Review summaries
	•	LLM-generated ratings
	•	Enable users to apply additional criteria and refine their shortlist.

Architecture Requirements
	•	Design an integrated and easy-to-debug solution.
	•	Use Reflex framework for UI, langchain for LLM integration, and skip the scraper code for now.
	•	Support multiple LLM models for summarization and rating generation.
	•	Use uv python package manager.
	•	Use python 3.12
	•	Consider chaining summarization and rating generation.

Expected Deliverables
	•	Skeleton code for the solution.
	•	Only write the skeleton code with docstrings and comments without any implementation.



please plot the weighted_average and average comparisons using random data in following formula. please generate random with rating from 1.00 to 5.00 and reviews_count from 1 to 200 
def calculate_weighted_rating(R, v, C, m_base=2):
    m = m_base * (5 - R)  # Dynamic prior weight
    return (v * R + m * C) / (v + m)

# For a filtered group (e.g., R ≥ 4.6):
filtered_listings = [listing for listing in all_listings if listing.rating >= 4.6]
C_filtered = sum(listing.rating for listing in filtered_listings) / len(filtered_listings)

# Compute weighted ratings for each listing in the filtered group:
for listing in filtered_listings:
    listing.weighted_rating = calculate_weighted_rating(
        listing.rating, 
        listing.reviews_count, 
        C_filtered
    )