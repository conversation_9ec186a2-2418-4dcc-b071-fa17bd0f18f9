# Airbnb Review Rating Chain – Async Architecture with LiteLLM & Loguru

This implementation defines an **async rating chain** for Airbnb listings that:

1. Summarizes reviews using an LLM via **LiteLLM**
2. Rates the summary using another LLM via **LiteLLM**
3. Uses **Loguru** for structured, developer-friendly logging
4. Supports parallel processing of listings using **asyncio**
5. Caches summaries and ratings using **DiskCache**

---

## 🔁 Chain Flow

```mermaid
flowchart LR
    A[Listings & Reviews] --> B[Check Summary Cache]
    B -- Miss --> C[Generate Summary via LLM]
    C --> D[Store Summary in Cache]
    B -- Hit --> D
    D --> E[Check Rating Cache]
    E -- Miss --> F[Generate Rating via LLM]
    F --> G[Store Rating in Cache]
    E -- Hit --> G
    G --> H[Combine Output: summary, rating, explanation]
    H --> I[Final JSON Output]
```

## Installation

```bash
pip install litellm loguru diskcache
```

## Implementation

```python
SUMMARY_PROMPT = """Summarize the following Airbnb guest reviews:
{REVIEWS_TEXT}

Summary:"""

RATING_PROMPT = """You are a quality evaluator. Based on the following summary, rate the Airbnb listing from 1 (worst) to 10 (best) and explain your reasoning.

Summary:
{SUMMARY_TEXT}

Rating and explanation:"""

from diskcache import Cache
from loguru import logger
import litellm
import asyncio
import re

cache = Cache("./review_chain_cache")


async def generate_summary(listing_id, reviews_text, ignore_cache=False):
    cache_key = f"summary:{listing_id}:{hash(reviews_text)}"
    if not ignore_cache and cache_key in cache:
        logger.debug(f"Cache hit for summary: {listing_id}")
        return cache[cache_key]
    
    prompt = SUMMARY_PROMPT.replace("{REVIEWS_TEXT}", reviews_text)
    logger.info(f"Generating summary for {listing_id}")

    try:
        response = await litellm.acompletion(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=300,
            temperature=0.7,
        )
        summary = response["choices"][0]["message"]["content"]
        cache[cache_key] = summary
        return summary
    except Exception as e:
        logger.error(f"Failed to generate summary for {listing_id}: {e}")
        return None


async def generate_rating(listing_id, summary, ignore_cache=False):
    if summary is None:
        return None, None

    cache_key = f"rating:{listing_id}:{hash(summary)}"
    if not ignore_cache and cache_key in cache:
        logger.debug(f"Cache hit for rating: {listing_id}")
        return cache[cache_key]

    prompt = RATING_PROMPT.replace("{SUMMARY_TEXT}", summary)
    logger.info(f"Generating rating for {listing_id}")

    try:
        response = await litellm.acompletion(
            model="claude-instant-1",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.7,
        )
        output = response["choices"][0]["message"]["content"]
        rating_match = re.search(r"\b([1-9]|10)\b", output)
        rating = int(rating_match.group(1)) if rating_match else None
        cache[cache_key] = (rating, output)
        return rating, output
    except Exception as e:
        logger.error(f"Failed to generate rating for {listing_id}: {e}")
        return None, None


async def process_listing(listing, ignore_cache=False):
    listing_id = listing["id"]
    reviews_text = "\n".join(listing["reviews"])

    summary = await generate_summary(listing_id, reviews_text, ignore_cache)
    rating, explanation = await generate_rating(listing_id, summary, ignore_cache)

    return {
        "reviews_summary": summary,
        "rating_summary": explanation,
        "rating": rating
    }


async def process_listings(listings, ignore_cache=False):
    tasks = [process_listing(listing, ignore_cache) for listing in listings]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    output = {}
    for listing, result in zip(listings, results):
        if isinstance(result, Exception):
            logger.error(f"Exception for {listing['id']}: {result}")
            output[listing["id"]] = {
                "reviews_summary": None,
                "rating_summary": None,
                "rating": None
            }
        else:
            output[listing["id"]] = result
    return output


def run_review_chain(listings, ignore_cache=False):
    logger.info("Starting Airbnb Review Rating Chain...")
    return asyncio.run(process_listings(listings, ignore_cache))


if __name__ == "__main__":
    listings = [
        {
            "id": "listing_101",
            "reviews": [
                "Amazing stay! Very clean and centrally located.",
                "Great host, but stairs were steep."
            ]
        },
        {
            "id": "listing_102",
            "reviews": [
                "Noisy at night, but spacious room.",
                "Excellent location and comfortable bed."
            ]
        }
    ]

    results = run_review_chain(listings)
    for lid, res in results.items():
        logger.info(f"\nListing: {lid}\n{res}\n")
```

## Output

```json
{
    {
  "listing_101": {
    "reviews_summary": "The apartment was clean, central, with a helpful host. Some mention steep stairs.",
    "rating_summary": "Rating: 8/10. Overall positive with minor accessibility concern.",
    "rating": 8
  },
  "listing_102": {
    "reviews_summary": "Guests appreciated the space and location, but noted nighttime noise.",
    "rating_summary": "Rating: 7/10. Great comfort, with some noise drawbacks.",
    "rating": 7
  }
}
```






