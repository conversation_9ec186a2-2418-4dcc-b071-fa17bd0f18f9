🧭 Crawl4AI Data Extraction Architecture — Template

---

## # Executive Summary
**Goal:**  
Design a **Crawl4AI-based pipeline** to extract structured data from web pages.  
The system focuses *solely* on:
- Requesting and crawling URLs (listing + detail pages)
- Handling pagination and link discovery
- Extracting structured content (via selectors or schema mapping)

**Key Exclusions:**  
No persistence, validation, transformation, orchestration, or downstream ETL logic.  
Only URL requests, Crawl4AI configuration, and data extraction.

**Scope Summary:**
- Input: One or more search/listing URLs.
- Engine: Crawl4AI for crawl orchestration and extraction.
- Output: Structured JSON adhering to user-defined schema.

---

# Architecture Overview

## ## Component Diagram (Mermaid)
```mermaid
flowchart TD
    A[Input URLs / Seeds] --> B[Crawl4AI Controller]
    B --> C[Page Downloader (HTTP/Headless)]
    C --> D[HTML Parser / Extractor]
    D --> E[Schema Mapper]
    E --> F[Structured JSON Output]
    B --> G[Pagination Handler]
    G --> B
```

## ## Sequence Diagram (Mermaid)
```mermaid
sequenceDiagram
    participant User
    participant Crawl4AI
    participant Downloader
    participant Extractor

    User->>Crawl4AI: Provide start URLs + selectors
    Crawl4AI->>Downloader: Fetch page content (HTML/Headless)
    Downloader-->>Crawl4AI: Return page content
    Crawl4AI->>Extractor: Apply selectors / AI extraction
    Extractor-->>Crawl4AI: Return structured data
    Crawl4AI->>Crawl4AI: Follow next-page links (pagination)
    Crawl4AI-->>User: Output structured JSON
```

---

# Technical Specification

## ## Crawl4AI Pipeline Flow
- Initialize Crawl4AI session with:
  - Start URLs
  - Depth / max pages
  - Rate limits
  - Pagination rule
- For each page:
  1. Fetch using Crawl4AI downloader.
  2. Parse HTML and apply selectors or AI extraction layer.
  3. Emit structured JSON record.
  4. Detect next-page URL and continue loop until exhaustion.

## ## Configuration & Parameters
| Parameter | Description | Example |
|------------|--------------|----------|
| `start_urls` | List of entry URLs to begin crawl | `["https://example.com/search?page=1"]` |
| `crawl_depth` | How deep Crawl4AI should follow links | `2` |
| `rate_limit` | Requests per second | `2` |
| `use_headless` | Boolean: enable browser rendering | `true` |
| `retry_attempts` | Number of retry attempts on failure | `3` |
| `pagination_selector` | CSS/XPath selector for next-page link | `"a.next::attr(href)"` |
| `selectors` | Dict of field selectors | `{ "title": "h1", "price": ".price", "url": "a::attr(href)" }` |

## ## Data Extraction Logic
- **Extraction Options:**
  - Use **CSS / XPath selectors**.
  - Use **AI-based structured extraction** (if Crawl4AI supports AIExtract or HBot schema builder).
- **Output Fields:**
  - Must map 1:1 to target schema fields.
- **Error Handling:**
  - If selector fails, attempt fallback selector or AI completion.
  - Log skipped fields with rule ID.

## ## Pagination Handling
- **Next-Page Discovery:** via selector or pattern.
- **Termination:** when next-page selector missing or duplicates detected.
- **Resumability:** checkpoint current URL index for retry.

## ## Input & Output Contracts
**Input**
```json
{
  "start_urls": ["https://example.com/search?page=1"],
  "selectors": {
    "title": "h1.title",
    "price": ".price",
    "location": ".address",
    "link": "a.details::attr(href)"
  },
  "pagination_selector": "a.next::attr(href)"
}
```

**Output**
```json
[
  {
    "title": "Cozy Apartment Downtown",
    "price": "$1200",
    "location": "Berlin",
    "url": "https://example.com/property/12345"
  }
]
```

## ## Resilience & Recovery
- Built-in Crawl4AI retry & delay features.
- Adaptive sleep between requests.
- Logging of skipped URLs and retry counts.
- Deduplication of visited URLs.

---

# Developer Assets

## ## Example Crawl4AI Config (YAML)
```yaml
start_urls:
  - "https://example.com/search?page=1"
crawl_depth: 2
rate_limit: 2
use_headless: true
pagination_selector: "a.next::attr(href)"
selectors:
  title: "h1.title"
  price: ".price"
  location: ".address"
  link: "a.details::attr(href)"
retry_attempts: 3
output_format: "json"
```

## ## Example Schema Mapping (JSON)
```json
{
  "schema_name": "PropertySchema",
  "fields": {
    "title": "string",
    "price": "string",
    "location": "string",
    "url": "string"
  }
}
```

---

# Short Questionnaire 

1. What is the **exact target schema** (fields, types, optionality)?  Pydantic
2. Provide **start URLs** or patterns to generate them dynamically.  start url is search url
3. Should Crawl4AI use **headless mode** for JS-rendered pages?  Use headless mode with setting so it can be changed flexibly 
4. Define **pagination selector** or pattern (e.g., `a.next::attr(href)` or page param).  Need to identify the optimal strategy for links / page extraction
5. Are there **rate-limit** or **proxy** requirements?  No proxy requirements 
6. Should data extraction rely on **CSS selectors**, **AI extraction**, or both?  Both
7. Is **HTML evidence logging** required (store snippet per record)?  yes
8. Define **output format**: JSON, NDJSON, or CSV?  JSON
9. Any **language or locale normalization** required (dates, currency, etc.)?  No

---