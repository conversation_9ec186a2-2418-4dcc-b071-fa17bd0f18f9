. Understand Python Syntax and Conventions
	•	Follow PEP 8 guidelines:
	•	Be mindful of whitespace usage (no extra spaces inside brackets or at line endings).
	•	Follow naming conventions:
	•	Classes: CamelCase
	•	Functions and variables: snake_case
	•	Constants: UPPER_CASE
	•	Modules and packages: short, snake_case
	•	Embrace Python idioms:
	•	Use built-in functions and dictionary comprehensions where appropriate.

2. Structure and Organization
	•	Use meaningful names for variables, functions, and classes.
	•	Organize code into modules and packages for logical separation.
	•	Ensure functions have a single responsibility.

3. Documentation and Comments
	•	Write docstrings for all functions, classes, and modules.
	•	Follow PEP 257 guidelines for docstring format (brief description, arguments, return values, and exceptions).
	•	Comment code where needed to explain complex logic or decisions, but avoid redundant comments.

4. Error Handling and Exceptions
	•	Use try-except blocks to handle errors gracefully.
	•	Raise and catch meaningful exceptions (ValueError, TypeError, etc.).
	•	Consider custom exceptions for specialized error conditions.

5. Logging with loguru
	•	Use loguru for logging.
	•	Configure logging to specify rotation, retention, and formatting.
	•	Utilize log levels (trace, debug, info, success, warning, error, critical).
	•	Provide clear, contextual log messages for easier debugging.
	•	Use “@logger.catch” for automatic exception logging.
	•	Include performance logging for time-critical sections.
	•	Filter or redact sensitive information in logs.

6. Edge Cases and Input Validation
	•	Validate input data types and ranges to handle unexpected inputs.
	•	Log errors when invalid data is encountered.
	•	Raise appropriate exceptions for invalid input.

7. Avoid code duplication
    •	Encapsulate common functionality in functions or classes.
    •	Utilize inheritance and composition for code reuse.
    •	Consider using decorators for common patterns.
    •	Follow DRY (Don't Repeat Yourself) principle for reducing code duplication.
    •	Use existing function functions and classes as needed.
    