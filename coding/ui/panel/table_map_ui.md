# Task: Implement Interactive Data Grid with Linked Map Component Panel Component

## Objective
Build a Panel component called DataGridWithMap featuring a bi-directionally linked data grid and map component. 
The solution must implement dynamic filtering, spatial synchronization, and state persistence.
Here is the Panel Reactive table example https://panel.holoviz.org/how_to/custom_components/examples/table_viewer.html
Use LeafMap for the map component https://leafmap.org/
## Core Requirements

### Bi-directional Synchronization
1. **Grid → Map Updates**
   - Filter/sort actions in grid must update visible map markers
   - Preserve spatial clustering during updates

2. **Map → Grid Updates**
   - Spatial interactions must filter grid data
   - Bounding box selections constrain coordinate ranges
   - Zoom-level changes adjust data density thresholds

### State Management
1. **Shared State Requirements**
   - Single source of truth for filtered dataset
   - Loading states during transitions

2. **Persistence**
   - Filter presets storage
   - Map position/zoom retention
   - Session restoration capabilities

## Implementation Constraints
- **Primary Libraries**:
  - Data Grid: Panel Tabulator
  - Mapping: LeafMap
  - State: Panel state management
- **Data Handling**:
  - Use Pandas for DataFrame operations
  - Implement efficient geo-filtering (R-tree indexing)
- **Performance**:
  - Virtualized grid rendering
  - Map marker clustering
  - Memoized component updates

## Acceptance Criteria
1. Full replication of reference implementation features
4. Comprehensive test coverage:
   - Cross-component synchronization
   - Edge case handling (empty states, invalid coords)

## Reference Implementation Notes
- Focus on core functionality:
  - Filter widget generation logic
  - Summary view toggle mechanism
  - Caching system behavior
  - Callback architecture


# Google Gemini Prom