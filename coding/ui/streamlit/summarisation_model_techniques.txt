# Summarisation UI in Streamlit

Please create a UI for text summarisation in Streamlit.

Sidebar:
- Select model provider
- Select model
- Select length of summary

Main:
- File selection for input text. Load last n files from ./data/input directory.
- Text input
- Input token count 
- But<PERSON> "Generate Summary" to summarise and save the summary to output file.
- Summary output. If the file is already summarised, show the summary from output file.
- Summary token count

# Log the input and output to a file ./data/summarisation directory with filename format as input_<model>_<input_file_name>.txt and output_<model>_<input_file_name>.txt

# Models Page
// please add a page to view all the models available under the provider.
// if possible extract details for each model like model version, model description, model input token count, model output token count, model price per 1M tokens.

#Display Summaries Page
// Please add a page to view all the summaries.
// Display all the summaries in the ./data/summarisation directory for selected input file. Summary file names start with output_<model>_<input_file_name>.txt
// Dropdown to select input file from ./data/input directory.
// Display the summary file name, model used for summarisation, summary and summary token count.

# Ratings Page
// Please add a page to generate and display ratings for the summaries and save the ratings to ./data/ratings directory.
// UI should have following features:
# Sidebar:
// Dropdown to select input file from ./data/input directory.
// Multiselect to select models from all enabled models under Models page.
// Dropdown to select ratings prompt from ./data/prompts directory.
# Main:
// Text area to display ratings prompt from selected ratings prompt file.
// Text area to display ratings for the selected summary.
// If summary for selected model is not available, please skip that model.
// Button "Generate Rating" to generate and display ratings for all the summaries for selected input file using selected models. 
// In case rating already exists use existing rating file.
// Save the ratings to ./data/ratings directory with filename format as ratings_<input_file_name>_<model>.txt
// Display ratings matrix with summary model as columns and rating model as rows.

# Ratings chat page
// Please add a page to chat using selected ratings prompt as system prompt and selected summary model as user prompt.
# Sidebar:
// Dropdown to select input file from ./data/input directory.
// Dropdown to select ratings prompt from ./data/prompts directory.
// Dropdown to select summary model from all enabled models under Models page.
# Main:
// Using expander display the content of ratings prompt file.
// Display chat and chat history using streamlit chat message component.
// Button "Send" to send the message to the chat.
// save the chat history to ./data/chat_history directory with filename format as rating_chat_history_<input_file_name>_<ratings_model>_<summary_model>.txt

// Please use the code from llm_provider to chat using the selected ratings model.



# Google Gemini Models
// please add support for google gemini models to models.py. 
// use example code for models.list from this url to list available models: https://ai.google.dev/api/models

# Add UI to enable selected models to be used for summarisation.
// under Models page, please add streamlit tags input to select models.
// only selected models should be used for summarisation.

# Streamlit caching
// please implement the caching for the user input selection in the sidebar.
// Cache the selected values of different widgets and inputs so that user doesn't have to reselect the values again on next load / run. 
// Only chache the selected values of the widgets and inputs in sidebar.
// store the cache in {DATA_DIR}/cache directory.
Notes: 
Windsurf didn't work event after 3 updates.
Aider implemented caching successfully with issues of Input file not correctly set even though file's content loaded correctly. Fixed after 1 update.


# LLMS util
// please add code query using langchain. if the provider is ollama, please use Ollama client.
