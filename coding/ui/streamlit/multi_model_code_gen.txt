# Multi Model Code Generation

You are an AI software architect. Your role is to architect a multi model code generation UI using streamlit.

The UI should have the following features:
1. It should have a sidebar with the following options:
    - Multiselect to select the models from a list of models.
    - Text area to enter the prompt or to specify the prompt file.
    - Button to generate the code.
2. It should have a main area with following sections:
    - System prompt section under expander
    - Generated code for each selected model under expander
    - A button to copy the generated code to clipboard.
    - A button to save the generated code to a file.

Please return the suggested architecture as markdown file.



# Multi-Model Code Generation UI Architecture

## Streamlit Application Structure

### Sidebar Components
1. **Model Selection**
   - `st.multiselect` widget with label "Select AI Models"
   - Default options: ["GPT-4", "Claude-2", "PaLM-2", "Llama-2"]
   - Minimum selection: 1 model required

2. **Prompt Input**
   - `st.text_area` with label "Enter Prompt" (height=150px)
   - `st.file_uploader` with label "OR Upload Prompt File" (accepts .txt only)
   - Validation: Text input takes precedence if both provided

3. **Generation Trigger**
   - `st.button` labeled "Generate Code" (centered)
   - Displays loading spinner during processing

### Main Interface Components
1. **System Prompt Display**
   - `st.expander` titled "System Prompt Configuration"
   - Non-editable text showing predefined system prompt:
     ```python
     "You are an expert developer. Generate clean, efficient code that follows best practices."
     ```

2. **Model Output Section**
   - Vertical container with expandable sections for each selected model
   - Each model section contains:
     - Header with model name/icon
     - `st.code_block` with syntax highlighting (language auto-detected)
     - Line numbers displayed by default

3. **Code Actions**
   - Per-model button group containing:
     - `st.button` with copy icon (📋) and tooltip "Copy to Clipboard"
       - Shows toast message "Copied!" on success
     - `st.button` with save icon (💾) and tooltip "Save to File"
       - Default filename: `{model_name}_generated.py`
       - Shows toast message "Saved!" on success

### Technical Requirements
1. Error handling for:
   - API connectivity issues
   - Model-specific rate limits
   - Invalid prompt formatting

2. Input Validation:
   - Prevent empty prompts
   - Require ≥1 model selection
   - Validate file type/encoding

3. State Management:
   - Cache model responses
   - Persist UI settings across sessions
   - Track generation timestamps

4. Output Formatting:
   - Automatic code formatting
   - Language-specific syntax highlighting
   - Responsive layout for code blocks