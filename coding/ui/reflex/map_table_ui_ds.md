# Task: Implement Interactive Data Grid with Linked Map Component in Reflex

## Objective
Build a Reflex web application featuring a bi-directionally linked data grid and map component that maintains feature parity with the provided Python reference implementation (ipydatagrid-based). The solution must implement dynamic filtering, spatial synchronization, and state persistence.

## Core Requirements

### Data Grid Component
1. **Dynamic Filter UI Generation**
   - Auto-detect column types (datetime, numeric, categorical, boolean)
   - Generate appropriate filter controls per column type:
     - Date pickers for datetime columns
     - Range sliders for numeric columns
     - Radio buttons (≤2 unique values)
     - Dropdowns (3-10 unique values)
     - Text search (>10 unique values)
     - Checkboxes for boolean columns

2. **Grid Features**
   - Server-side pagination
   - Multi-column sorting
   - Hyperlink rendering for specified columns
   - Summary view toggle for aggregated columns
   - Persistent filter state (localStorage API)

### Map Component
1. **Base Requirements**
   - Leaflet/OpenStreetMap integration
   - GeoJSON data visualization
   - Map interaction handlers:
     - Bounding box selection
     - Zoom level changes
     - Layer filtering

### Bi-directional Synchronization
1. **Grid → Map Updates**
   - Filter/sort actions in grid must update visible map markers
   - Preserve spatial clustering during updates

2. **Map → Grid Updates**
   - Spatial interactions must filter grid data
   - Bounding box selections constrain coordinate ranges
   - Zoom-level changes adjust data density thresholds

### State Management
1. **Shared State Requirements**
   - Single source of truth for filtered dataset
   - Debounced updates for performance (≤300ms delay)
   - Loading states during transitions

2. **Persistence**
   - Filter presets storage
   - Map position/zoom retention
   - Session restoration capabilities

## Implementation Constraints
- **Primary Libraries**:
  - Data Grid: AG Grid React (with Reflex wrapper)
  - Mapping: react-leaflet integration
  - State: Reflex context API + hooks
- **Data Handling**:
  - Use Pandas for DataFrame operations
  - Implement efficient geo-filtering (R-tree indexing)
- **Performance**:
  - Virtualized grid rendering
  - Map marker clustering
  - Memoized component updates

## Acceptance Criteria
1. Full replication of reference implementation features
2. Sub-100ms response time for all interactions
3. Mobile-responsive layout (grid + map column stacking)
4. Comprehensive test coverage:
   - Cross-component synchronization
   - Edge case handling (empty states, invalid coords)
   - Browser compatibility (Chrome, Firefox, Safari)

## Reference Implementation Notes
- Preserve core functionality from original Python code:
  - Filter widget generation logic
  - Summary view toggle mechanism
  - Caching system behavior
  - Callback architecture
- Adapt Jupyter widget patterns to Reflex event model
