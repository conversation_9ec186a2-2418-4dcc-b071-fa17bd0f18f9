# Interactive Map Panel Widget

## Description
Create a Panel-based widget that accepts a `Datastore` object as input. This widget must render an interactive map, automatically placing markers for each row in the `Datastore`. The map should be reactive, updating whenever the `Datastore` is filtered so that only the rows in `data_store.filtered_data` are displayed as markers.

## Instructions
1. **Input**: A `Datastore` object, which provides access to `data_store.filtered_data`.
2. **Behavior**: 
   - Render an interactive map with markers representing each row in `data_store.filtered_data`.
   - Automatically refresh the map’s markers whenever `filtered_data` changes.
3. **Implementation Details**:
   - Use **Python**, **ipyleaflet**, **Panel**, and **Param**.
   - Ensure that the final component is reactive, reflecting changes in real-time as filters are applied to the datastore.
4. **Output**: A function or class named `InteractiveMapComponent(data_store: Datastore)` that produces the interactive map widget.

## Deliverable
- Provide the code for `InteractiveMapComponent(data_store: Datastore)` in a self-contained manner, relying on the provided `Datastore` structure.

# Datastore
```python
cimport pandas as pd
import panel as pn
import param
from panel.viewable import Viewer

# Import the logger
from panel_test_app.logger import logger


class DatastoreWithFilters(Viewer):
    """
    A data store component that manages data and filter widgets.

    This component stores the base data, creates appropriate filter widgets based on
    data types, and provides filtered data access.
    """

    data = param.DataFrame(doc="Base data to store")
    filters = param.List(constant=True, doc="Columns to filter on")
    app_name = param.String(default="default_app", doc="Name used for caching")
    enable_persistence = param.Boolean(default=True, doc="Enable state persistence")

    # Add a parameter to store the result of the reactive filtering
    filtered_data = param.DataFrame(doc="The reactively filtered data")

    def __init__(self, **params):
        super().__init__(**params)

        # Create widgets based on data types and apply reactive filtering
        self._widgets = []
        self._create_filter_widgets()  # This sets self.filtered (reactive expression)

    def _create_filter_widgets(self) -> None:
        """Create filter widgets based on column data types and apply reactive filtering."""
        # Create a reactive dataframe
        dfx = self.param.data.rx()
        widgets = []

        for filt in self.filters:
            if filt not in self.data.columns:
                continue

            # Create appropriate widget based on data type
            if pd.api.types.is_object_dtype(self.data[filt]):
                options = dfx[filt].dropna().unique().tolist()
                widget = pn.widgets.Select(name=filt, options=options)
                condition = dfx[filt] == widget.rx()

            elif pd.api.types.is_datetime64_any_dtype(self.data[filt]):
                min_date = self.data[filt].min()
                max_date = self.data[filt].max()

                # Check if the datetime has time components by examining the dtype
                # datetime64[ns] has time components, date32/date64 doesn't
                has_time = "datetime64" in str(self.data[filt].dtype)

                if has_time:
                    # Use DatetimeRangeSlider for datetime with time components
                    widget = pn.widgets.DatetimeRangeSlider(
                        name=filt,
                        start=min_date,
                        end=max_date,
                        value=(min_date, max_date),
                        format="%Y-%m-%d %H:%M:%S",
                    )
                else:
                    # Use DateRangeSlider for date-only columns
                    widget = pn.widgets.DateRangeSlider(
                        name=filt,
                        start=min_date,
                        end=max_date,
                        value=(min_date, max_date),
                        format="%Y-%m-%d",
                    )

                condition = dfx[filt].between(*widget.rx())

            elif pd.api.types.is_float_dtype(self.data[filt]):
                min_val = float(self.data[filt].min())
                max_val = float(self.data[filt].max())
                step = (max_val - min_val) / 100 if max_val != min_val else 1
                widget = pn.widgets.RangeSlider(
                    name=filt,
                    start=min_val,
                    end=max_val,
                    value=(min_val, max_val),
                    step=step,
                )
                condition = dfx[filt].between(*widget.rx())

            elif pd.api.types.is_integer_dtype(self.data[filt]):
                min_val = int(self.data[filt].min())
                max_val = int(self.data[filt].max())
                widget = pn.widgets.RangeSlider(
                    name=filt,
                    start=min_val,
                    end=max_val,
                    value=(min_val, max_val),
                    step=1,
                )
                condition = dfx[filt].between(*widget.rx())

            elif pd.api.types.is_bool_dtype(self.data[filt]):
                options = ["True", "False", "Both"]
                widget = pn.widgets.Select(name=filt, options=options, value="Both")
                # Special handling for boolean with "Both" option
                # Fix boolean handling to avoid Series truth value ambiguity
                condition = (widget.rx() == "Both") | (
                    dfx[filt] == (widget.rx() == "True")
                )

            else:
                # Default to MultiChoice for other types
                options = dfx[filt].dropna().unique().tolist()
                widget = pn.widgets.MultiChoice(name=filt, options=options)
                # Fix MultiChoice handling to avoid Series truth value ambiguity
                condition = dfx[filt].isin(
                    widget.rx().rx.where(widget.rx().rx.len() > 0, options)
                )

            # Apply the filter condition
            dfx = dfx[condition]
            widgets.append(widget)

        self.filtered = dfx
        self._widgets = widgets

    def __panel__(self):
        """Return the Panel layout for this component."""
        reset_button = pn.widgets.Button(name="Reset Filters", button_type="warning")
        reset_button.on_click(lambda event: self.reset_filters())

        return pn.Column(
            "## Filters", *self._widgets, reset_button, sizing_mode="stretch_width"
        )


class FilterView(Viewer):
    """
    Base class for components that need access to filtered data.
    """

    data_store = param.ClassSelector(class_=DatastoreWithFilters, doc="Data store")

    def __panel__(self):
        """Default panel implementation."""
        return pn.Column(
            "## Filtered Data",
            pn.pane.DataFrame(self.data_store.filtered),
        )

```