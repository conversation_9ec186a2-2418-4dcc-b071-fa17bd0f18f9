# Search Radius
please help write python code to filter a pandas dataframe based on longitude and latitude cols.
please implement a function called search_radius that takes a dataframe, a longitude and latitude column, and a radius in kilometers.
the function should return a new dataframe with the rows that are within the radius of the given longitude and latitude.

#Refined

Write a Python function called `search_radius` that filters a pandas DataFrame to return rows that are within a given radius from a specified geographic point.

Ensure to use the Haversine formula to calculate the geographical distance.

The function, `search_radius`, should accept:
1. **A pandas DataFrame** with at least two columns for longitude and latitude.
2. **Longitude and latitude values** representing the center point from which distances are calculated.
3. A **radius in kilometers** to filter rows within that distance.

Your task is to write a complete implementation of this function, including any necessary imports or formulas.

# Steps
1. **Import necessary libraries**: Make sure to use pandas and either NumPy or Python's math library.
2. **Calculate distances using the Haversine formula** to determine which rows fall within the given radius.
3. **Filter the DataFrame**: Use boolean indexing to create a new DataFrame containing rows that are within the specified distance.

# Output Format
The function should return the resulting filtered DataFrame that only contains rows that are within the radius provided.

# Examples
```python
import pandas as pd

# Sample DataFrame
data = {
    'longitude': [30.5236, 31.2357, 29.9511],
    'latitude': [50.4501, 30.0444, 31.2318]
}
df = pd.DataFrame(data)

# Call the function
center_longitude = 30.0
center_latitude = 50.0
radius_km = 500

# Filter data within 500 km from the given coordinates
filtered_df = search_radius(df, center_longitude, center_latitude, radius_km)

print(filtered_df)
```
(Replace with realistic coordinates representing your expected use case if appropriate.)