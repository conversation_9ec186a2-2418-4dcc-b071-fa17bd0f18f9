# Search drawn shape
please help me draw a shape on a map and then search for points within that shape.
each point has a longitude and latitude. 
please help me write the python code to do this.

# Refined
Generate Python code that allows a user to draw a shape on a map and search for points of interest within that shape, based on their longitude and latitude.

You may use a combination of mapping libraries (such as Folium or Shapely) and instructions such as to capture user input and define points. The goal is to generate the correct Python tools necessary for the user to draw a region on a map and to find all points that fall within it. 

# Steps

1. **Required Libraries**: Import necessary libraries such as Folium (for drawing shapes on a map), ipyleaflet and Shapely (for defining geometric queries such as points within a shape).
2. **Map Creation**: Create an interactive map, using Folium to allow the user to define and draw a shape (such as a polygon) on it.
3. **Capturing Points**: Define a set of interest points with specific latitudes and longitudes.
4. **Shape Interaction**: Allow the user to draw a shape (polygon or similar) on the map.
5. **Shape and Point Processing**: With the drawn shape, utilize the Shapely library to analyze which points fall within the drawn region.
6. **Results Output**: Print or provide the necessary list of points that are within the shape.

# Output Format

Provide the complete Python code. Ensure that the code follows a logical sequence:
- Begin by importing the necessary libraries.
- Continue by creating a map and allowing a user to draw a shape.
- Use appropriate loops or conditions to check which points fall within the defined shape.

# Example

```python
# Step 1: Import necessary libraries
import folium
from folium.plugins import Draw
from shapely.geometry import Point, Polygon

# Sample Data: Points of Interest
points = [
    {"name": "Point A", "latitude": 40.7128, "longitude": -74.0060},
    {"name": "Point B", "latitude": 40.7138, "longitude": -74.0070},
    {"name": "Point C", "latitude": 40.7158, "longitude": -74.0080},
    {"name": "Point D", "latitude": 40.7168, "longitude": -74.0090}
]

# Step 2: Create a folium map
m = folium.Map(location=[40.7128, -74.0060], zoom_start=13)

# Step 3: Add drawing features (user should be able to draw on the map)
draw = Draw(export=True)
draw.add_to(m)

# Displays the folium map
m
