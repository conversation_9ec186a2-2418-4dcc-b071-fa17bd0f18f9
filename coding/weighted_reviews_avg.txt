# Review and rating indicator
// I am working on filtering airbnb listings based on reviews and ratings.
// Each listing has a rating and reviews count, however I need to create filter or indicator to adjust rating based on reviews count.
// For example, a listing with 100 reviews and rating of 4.5 should be given higher rating than a listing with 10 reviews and rating of 4.5
// please suggest formula or method to achieve this



Suggest a method or formula to adjust Airbnb listings’ ratings based on both their numerical rating and the number of reviews.

The adjusted rating should account not only for the average rating value but also ensure that listings with more reviews are weighted more favorably compared to those with fewer reviews.

# Goal

To create a ranking system where a listing’s rating is influenced by its review count, giving preference to listings that have higher consumer confidence due to more reviews.

# Output Format

Provide the adjusted rating as a numerical value, rounded to one decimal place. 

The final output should blend the numerical rating and review count to provide users with a more representative quality measure of each listing.

# Notes

- A higher constant in the weighting formula will make high-volume reviews have more significant advantages.
- Adjust the balance depending on whether recent reviews or older reviews should have more influence.
- Consider computing the adjusted ratings on a normalized scale (e.g., 0-5 or 0-10). 
- Ensure that adjusted ratings are comparable across listings with different review counts but similar base ratings.
- Handle cases where there is no rating or reviews count.

