# Coding Rules

Prompt:
You are an AI software development team leader. Define a concise set of baseline coding rules that all developers must follow, focusing on:

1. **Linting**  
   - Specify a standard linter (e.g., Flake8 or ESLint) and enforce its usage for all code.

2. **Formatting**  
   - Adopt a consistent code formatter (e.g., <PERSON> or <PERSON><PERSON><PERSON>) to ensure uniform style.

3. **Logging**  
   - Use LogGuru exclusively for logging.  
   - Store all log files in the `logs` directory at the project root.

4. **Type Hinting**  
   - Require explicit type hints for all functions and methods for clarity and maintainability.

5. **Docstrings**  
   - Write concise docstrings for every class, function, and method, following a clear format (e.g., Google or reStructuredText).

6. **Object-Oriented Coding**  
   - Emphasize OOP principles with clear class structures, avoiding large monolithic files.

7. **File Length Management**  
   - Limit file size; split functionalities into smaller, logical modules instead of creating excessively long files.

8. ** Include additional best practices you think might form the basis of good coding practices **  

Make each rule direct and specific, with no overly detailed explanations. These guidelines serve as the core coding principles for the entire team, allowing space for additional, specialized rules by sub-teams or frameworks as needed.




#  Rules
1. Linting
	•	Use Flake8 (Python) or ESLint (JavaScript) in every project.
	•	Run lint checks before committing code.

2. Formatting
	•	Always run Black (Python) or Prettier (JavaScript/TypeScript) on all files.
	•	Enforce consistent indentation, line length, and spacing rules.

3. Logging
	•	Use LogGuru exclusively for all logging.
	•	Store log files in the logs directory at the project root.
	•	Ensure log messages are clear and facilitate easy debugging
	•	Use appropriate log levels (trace, debug, info, success, warning, error, critical)
	•	Add context to logs where necessary (e.g., @logger.catch for error tracking)
	•	Configure log formatting for readability and consistency


4. Type Hinting
	•	Provide explicit type hints for every function and method.
	•	Maintain consistency in type definitions across modules.
	•	Use `mypy` for static type checking (zero errors enforced).  
	•	Avoid `Any` unless strictly necessary.

5. Docstrings
	•	Write concise docstrings for every class, function, and method.
	•	Follow a standardized format (e.g., Google-Style or reStructuredText).

6. Object-Oriented Coding
	•	Organize code into clear class structures.
	•	Avoid large monolithic files; each class or related classes in separate files.

7. File Length Management
	•	Keep files below a reasonable limit (e.g., 500 lines).
	•	Split functionalities into logical modules.

8. Additional Best Practices
	•	Write unit tests.
	•	Store secrets and configuration in environment variables, not in code.
	•	Avoid magic numbers; define constants with meaningful names.
    •	Use uv for dependency management and packaging.