# Disk Usage Script
Please write a python script that will take the following input parameters and output a json file with the disk usage analysis results.
Input parameters:
- root_path: str, the root path to start the disk usage analysis
- threshold_mb: int, the threshold in MB to consider a directory as large
- unit: str, the unit to use for the output, can be mb or gb
- max_depth: int, the maximum depth to traverse the directory tree
Output:
- A json file with the disk usage analysis results
sample output:
{
    "path": "/home/<USER>/large_directory",
    "size": 1024,
    "files": [
        {"name": "file1.txt", "size": 100},
        {"name": "file2.txt", "size": 200}
    ],
    "children": [
        {"path": "/home/<USER>/large_directory/child_directory", "size": 100, "files": [{"name": "file1.txt", "size": 100}, {"name": "file2.txt", "size": 200}]},
        {"path": "/home/<USER>/large_directory/child_directory2", "size": 200, "files": [{"name": "file1.txt", "size": 100}, {"name": "file2.txt", "size": 200}]}
    ]
}
// Only add files to the output if the size is greater than the threshold
// Only add files and children to the output if the size is greater than the threshold


# Disk usage pie chart
Please write a python script that will take the following input parameters and output a pie chart using plotly with the disk usage analysis results.
Input parameters:
    - disk_usage_json: str, the path to the disk usage json file

// Pie chart should only display the top level directories that exceed the threshold size
// On clicking a pie chart segment, pie chart should updaye to display the corresponding directory's children

