# Forms Implementation using python and sveltekit

# I am looking to implement a form using python and sveltekit.
# I need to create a form that can be used to collect data from a subject.
# The form contains multiple sections, each with multiple questions.
# each question is a multiple choice question. 
# questions requires following types of input:
# - dropdown list of options.
# - rating scale from 1 to 5.
# - yes/no.
# - short text answer.

# Implementation should be dynamic and should be able to handle any number of sections and questions.
# it should be simple to add new sections and questions.
# form should have validation setting VALIDATE_FORM. If it is set to True, it should validate the form and make sure user completes all required questions if it is required.
# answers should be stored in a file on server with following format:
# health_questionnaire_{unique_id}.json
# please also save the questionnaire schema that was taken in the same file. <PERSON>hema would help load the questionnaire later even if the questionnaire changes.

# please provide the implementation details and code.



# Dynamic Form Implementation using Python and SvelteKit

## Objective
Create a dynamic form system using Python (backend) and SvelteKit (frontend) to collect data from subjects. 
The system should be flexible, allowing easy addition of new sections and questions.

## Form Structure
- Multiple sections, each containing multiple questions
- Questions types:
  1. Dropdown list
  2. Rating scale (1 to 5)
  3. Yes/No
  4. Short text answer

## Key Requirements
1. Dynamic Implementation:
   - Handle any number of sections and questions
   - Easy to add new sections and questions

2. Validation:
   - Implement a VALIDATE_FORM setting
   - When True, ensure all required questions are completed

3. Data Storage:
   - Store answers in JSON format on the server
   - File naming convention: `health_questionnaire_{unique_id}.json`
   - Include the questionnaire schema in the same file for future reference

## Technical Specifications
- Backend: Python
- Frontend: SvelteKit
- Data Format: JSON

## Deliverables
1. Backend API (Python):
   - Endpoints for fetching questionnaire schema
   - Endpoint for submitting form data
   - Logic for data validation and storage

2. Frontend Implementation (SvelteKit):
   - Dynamic form rendering based on schema
   - Client-side validation
   - Form submission handling

3. Data Model:
   - JSON schema for questionnaire structure
   - JSON schema for form responses

4. Documentation:
   - API documentation
   - Instructions for adding new sections/questions
   - Example of questionnaire schema and stored data

Please provide implementation details, code snippets, and any additional considerations for this system.



# Test form
please create a dynamic form test app using SvelteKit and svelte-formly.
app should have form with with different question types.
app should showcase dynamic questions functionality where new questions are displayed based on the answer to the previous question.


# Refined prompt
Create a SvelteKit app that dynamically generates a form using `svelte-formly`. The form should include multiple types of questions and demonstrate functionality such that new questions can be conditionally displayed based on user responses.

# Steps

1. **Set Up SvelteKit and Install Dependencies:**
   - Create a new SvelteKit project:
     ```bash
     npm init svelte@next your-app-name
     ```
   - Install the necessary dependencies including `svelte-formly`:
     ```bash
     npm install svelte-formly
     npm install --save-dev sveltekit
     ```

2. **Create the Form Component:**
   - Use `svelte-formly` to create a form with diverse question types such as text, select, checkbox, and radio.
   - Define form fields with conditions to display the next question based on the answer provided by the user.

3. **Define Logic for Dynamic Questions:**
   - Configure `svelte-formly` options to determine when specific questions are shown. Use the `visible` property or conditional callbacks for questions that should only appear after a previous one is answered in a certain way.

4. **Integrate into SvelteKit App:**
   - Embed the dynamic form component inside a SvelteKit page and create navigation to this form page.
   - Set up any required data handling such as state for form submission and validation.

# Output Format

The output should be SvelteKit code that includes:
- A `Form.svelte` component that uses `svelte-formly` to display multiple types of questions (e.g., text input, dropdown, etc.)
- Implemented dynamic logic where certain questions only appear based on previous answers (e.g., a "Yes/No" answer conditionally displaying unique follow-up questions).
- Concise but complete demonstration of dynamic question functionality.
  
Make sure to include specific sections for:
- **Dependencies**: All necessary SvelteKit and `svelte-formly` dependencies.
- **Form Definition**: The structure of the forms and the configuration for dynamic questions.
- **Dynamic Logic Example**: Example code snippets that show how questions are conditionally displayed.

# Examples

Here is an example:

### Component: Form.svelte
```html
<script>
  import { SvelteFormly } from 'svelte-formly';
  let formModel = [
    {
      type: 'text',
      key: 'name',
      label: 'What is your name?'
    },
    {
      type: 'radio',
      key: 'likePets',
      label: 'Do you like pets?',
      options: ['Yes', 'No']
    },
    {
      type: 'text',
      key: 'petPreference',
      label: 'What is your favorite pet?',
      visible: model => model.likePets === 'Yes'
    },
  ];
</script>

<SvelteFormly {formModel} />
```

(Real examples should include more questions involving multiple fields such as checkboxes and dropdowns to fully showcase dynamic capabilities, tailored to user interactions.)

