# Add logs to the code
# please add logs to the code using loguru library.
# please replace all the print statements with loguru logs.
# please make sure suitable log levels are used.
# please log the messages in a way that they are easy to understand and debug.
# please log the function names, class names, module names etc.
# please log the variables, arguments, return values etc.
# please log the errors, exceptions, tracebacks etc.
# please log the warnings, info, debug messages etc.
# please log the critical, error, warning, info, debug, traceback messages etc.

# Refined using claude-3.5-sonnet-20240620

Add logging to code using Loguru

Replace all print statements with Loguru logs

Implement comprehensive logging:
- Ensure log messages are clear and facilitate easy debugging and monitoring
- Add context to logs where necessary 
- Use appropriate log levels (trace, debug, info, success, warning, error, critical)

Configure log formatting for readability and consistency
Set up log rotation and retention policies if applicable
Consider adding performance logging for critical sections
Implement log filtering or silencing for sensitive information
Add timing information for long-running operations

Implement comprehensive logging:

Function names, class names, and module names
Variables, arguments, and return values
Errors, exceptions, and tracebacks
Warnings and informational messages