# Refined Prompt: Multi-Agent Software Development System

Please create a comprehensive guide for developing a multi-agent software development system responsible for managing the entire end-to-end software development lifecycle. The system should specifically comprise the following clearly defined agents:

- **Project Manager Agent**
- **Software Architect Agent**
- **Software Engineer Agent**
- **Tester Agent**
- **Quality Assurance (QA) Agent**

## Roles and Responsibilities:

### Project Manager Agent
- Responsible for gathering project requirements.
- Seeks clarification for ambiguous or incomplete requirements.
- Collaborates with the Software Architect Agent to create and define tasks.
- Allocates tasks to appropriate agents (Software Engineer, Tester, QA).
- Coordinates task completion, manages workflow, and consults with other agents as needed.
- Marks tasks as accomplished upon completion and approval.

### Software Architect Agent
- Analyzes and understands project requirements.
- Conducts thorough research to select appropriate frameworks, libraries, and technologies.
- Designs high-level system architecture, creating clearly defined modules and skeleton code.
- Generates detailed implementation tasks and returns these tasks to the Project Manager Agent.

### Software Engineer Agent
- Implements code based on tasks assigned by the Project Manager Agent, strictly following guidelines provided by the Software Architect Agent.
- Upon completion of coding tasks, returns tasks to the Project Manager Agent for further processing.

### Tester Agent
- Writes comprehensive tests based on tasks received from the Project Manager Agent.
- Performs thorough testing to validate that all implemented code meets specified requirements.
- Returns tested tasks back to the Project Manager Agent.

### Quality Assurance (QA) Agent
- Conducts quality checks and ensures the implemented system aligns with the initial requirements and predefined quality standards.
- Returns validated tasks back to the Project Manager Agent upon successful quality assessment.

## Research and Implementation Guidelines:

Conduct extensive research on efficient methodologies for designing and implementing this multi-agent system. The system should:

- Be **model-agnostic**, capable of integrating multiple, distinct AI models tailored to each agent type.
- Identify suitable frameworks and libraries explicitly designed for multi-agent systems development.
- Recommend best practices and architectural patterns that ensure efficient communication, task management, and seamless integration across agents.

Please provide a clear, high-level document outlining:
- Recommended frameworks and libraries (emphasizing model-agnostic solutions).
- Architectural design considerations for seamless agent collaboration.
- Key implementation steps and workflow management strategies to optimize system efficiency.

