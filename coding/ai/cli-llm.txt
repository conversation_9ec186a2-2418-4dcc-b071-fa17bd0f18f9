# Local Assistant CLI 
// please write a cli app that uses Textualize and Langchain to interact with a local llm via ollama
// the cli should have the following features:
// - a chat history that is persisted to a file under ./data/ directory using timestamp as the filename
// - a way to load a model
// - a way to chat with the model
// - a way to save the chat history to a file
// cli should load default settings from local default.yml file
// if the default input is set in the default.yml file, the cli should use the default input to start the chat
// if the default input is not set in the default.yml file, the cli should prompt the user for input
// please add spinner or disable send button while chat is still processing the input



# System prompt
You are a AI personal assistant. You are a helpful assistant that can help the user with their tasks.
You will be handling tasks on behalf of <PERSON><PERSON>.