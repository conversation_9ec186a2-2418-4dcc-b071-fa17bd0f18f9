Please help me generate two `CLAUDE.md` files for Python development, based on [Anthropic's Claude memory best practices](https://docs.anthropic.com/en/docs/claude-code/memory):

1. **Project-Specific `CLAUDE.md`** – tailored to an individual Python project and its unique coding conventions or tooling.  
2. **Global `CLAUDE.md`** – a shared, reusable coding guide that applies across multiple Python projects, serving as a baseline for consistency.

### Topics to Include

- **Python Coding Style**  
  Naming conventions, PEP8 guidance, docstrings, and type hints.

- **Dependency Management**  
  Use of `uv` package manager for handling environments and dependencies.

- **Testing**  
  Strategy and conventions for using `pytest`, including folder structure and naming patterns.

- **Linting & Formatting**  
  Use of `ruff` for linting, formatting rules, autofix usage, and integration.

### Please Also Cover

- Recommended project layout (e.g., `src/`, `tests/`, `scripts/`)
- Use of `.env` files for managing environment-specific configuration
- Version control practices (e.g., `.gitignore`, commit message style)
- Pre-commit hook usage
- How `CLAUDE.md` can assist LLMs (<PERSON>, <PERSON><PERSON><PERSON><PERSON>, etc.) in aligning with developer preferences and conventions

### Formatting Guidelines

- Structure both files with clear sections and markdown headings
- Use concise bullet points and brief rationale where helpful
- Clearly differentiate between what belongs in the *project-specific* file vs. the *global* file
