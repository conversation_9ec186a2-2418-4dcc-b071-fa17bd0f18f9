# Prompt Evaluation Benchmark – Short Technical Spec

**Audience:** Lead Architect  
**Goal:** Enable architecture & implementation design for a reproducible, double‑blind prompt evaluation benchmark across multiple LLMs.

---

## 1) Purpose & Scope
Run prompt‑variant × LLM experiments with replication, automated validation, double‑blind LLM‑as‑Judge scoring, weighted aggregation, and reproducible logs. Output ranked, per‑LLM recommendations and side‑by‑side result tables.

**Out of scope:** UX dashboards (beyond static reports), training fine‑tunes.

---

## 2) Core Entities & Minimal Schemas
All IDs are UUIDv4. Timestamps are ISO‑8601. Suggested storage: relational DB + object store for artifacts.

### 2.1 RunManifest (YAML/JSON)
```json
{
  "run_id": "uuid",
  "created_at": "ts",
  "task_suite": ["task_id", "..."],
  "prompt_variants": ["prompt_id", "..."],
  "targets": ["model_id", "..."],
  "replicates": 3,
  "decoding_defaults": { "temperature": 0.2, "top_p": 1.0, "seed": 13 },
  "randomization_seed": 42,
  "judge_pool": ["judge_model_id_1", "judge_model_id_2"],
  "evaluation_plan": { "rubric_version": "v1", "weights_profile": "default-v1" }
}
```

### 2.2 Task
```json
{
  "task_id": "uuid",
  "name": "summarization-short-news",
  "spec": "text of task instructions",
  "acceptance_criteria": ["bullet1", "bullet2"],
  "ground_truth": { "optional": true },
  "validators": ["validator_id", "..."],
  "inputs": [{ "input_id": "uuid", "payload": { "text": "..." } }]
}
```

### 2.3 PromptVariant
```json
{
  "prompt_id": "uuid",
  "strategy_label": "Structured Checklist | Few-shot Focused | ...",
  "prompt_text": "templated text with {INPUT} etc.",
  "placeholders": ["INPUT","STYLE","CONSTRAINTS"],
  "anonymized_hash": "sha256"
}
```

### 2.4 TargetModel
```json
{
  "model_id": "uuid",
  "provider": "openai|anthropic|...",
  "name": "gpt-5",
  "version": "2025-07-30",
  "decoding_params": { "temperature": 0.2, "top_p": 1.0 },
  "cost_estimator": { "prompt_per_1k": 0.0, "completion_per_1k": 0.0 }
}
```

### 2.5 Generation
```json
{
  "gen_id": "uuid",
  "run_id": "uuid",
  "task_id": "uuid",
  "input_id": "uuid",
  "prompt_id": "uuid",
  "model_id": "uuid",
  "replicate_ix": 1,
  "seed": 123,
  "output": { "text": "..." },
  "token_usage": { "prompt": 0, "completion": 0, "total": 0 },
  "latency_ms": 0,
  "status": "ok | error | timeout",
  "error": "nullable"
}
```

### 2.6 ValidatorResult
```json
{
  "val_id": "uuid",
  "gen_id": "uuid",
  "validator_id": "uuid",
  "name": "json_schema | unit_test | regex | tolerance",
  "passed": true,
  "details": { "failures": [], "metrics": { "f1": 0.92 } }
}
```

### 2.7 JudgePacket & JudgeScore (Double‑Blind)
```json
{
  "packet_id": "uuid",
  "gen_id": "uuid",
  "visible_prompt_text": "anonymized prompt text",
  "task_spec": "visible spec",
  "acceptance_criteria": ["..."],
  "validator_summary": { "json_schema": true, "unit_tests": 5 },
  "hidden": { "prompt_id": "uuid", "model_id": "uuid" }
}
```
```json
{
  "score_id": "uuid",
  "packet_id": "uuid",
  "judge_model_id": "uuid",
  "metrics": {
    "accuracy": 90, "adherence": 96, "completeness": 94,
    "clarity": 90, "conciseness": 85, "creativity": 70
  },
  "notes": "1–2 sentence rationale per metric",
  "mean_score": 90.0
}
```

### 2.8 AggregateScore
```json
{
  "agg_id": "uuid",
  "run_id": "uuid",
  "prompt_id": "uuid",
  "model_id": "uuid",
  "task_id": "uuid",
  "replicate_mean": 88.4,
  "replicate_std": 2.1,
  "replicate_se": 1.2
}
```

---

## 3) System Components
1. **Orchestrator** – Reads RunManifest, expands to job graph, manages retries/backoff, and enforces replication.
2. **Randomizer/Blinder** – Shuffles prompt/order, hashes Prompt_IDs; builds JudgePackets without provenance.
3. **Executors (LLM Runners)** – Unified adapter to call target models with pinned params and seeds.
4. **Validators** – Pluggable rule‑based checks (JSON schema, regex, counters, unit tests, numeric tolerance, length caps).
5. **Judging Service** – Multi‑judge execution (≥2 families), rubric anchoring, integer 0–100 outputs, rationale capture.
6. **Aggregator/Stats** – Weighted means, CI via bootstrap or replicate SE, tie logic.
7. **Storage Layer** – SQL (entities/indices) + object store (artifacts, packets, raw outputs).
8. **Reporting** – Reproducible CSV/Parquet, Markdown tables, and per‑LLM rankings.
9. **CLI/SDK** – `bench run`, `bench resume`, `bench report`, `bench export`.

---

## 4) Workflow (Happy Path)
1. **Ingest**: Validate RunManifest; freeze configs (content‑hash).
2. **Plan**: Expand (task × input × prompt × model × k) matrix; seed PRNG.
3. **Generate**: Submit jobs to Executors with idempotency keys; persist Generation.
4. **Validate**: Run Validators; persist ValidatorResults; gate judging.
5. **Judge**: Build blinded JudgePackets; run judges; persist JudgeScores.
6. **Aggregate**: Compute per‑pair replicate means; compute CIs; apply tie policy.
7. **Report**: Emit side‑by‑side tables and per‑LLM best‑prompt rankings; include NA rows + next‑step notes.
8. **Finalize**: Write run summary, cost/latency, and artifact index; sign manifest + checksums.

**Failure Handling:** Mark missing metrics N/A with reason; queue resubmission with exponential backoff; maintain audit trail.

---

## 5) Rubrics, Weights, & Policies
- **Metrics:** accuracy(25), adherence(20), completeness(20), clarity(15), conciseness(10), creativity(10 if applicable; else renormalize).
- **Judging Rules:** integer [0–100]; cite passed/failed criteria; do not infer or identify models/authors.
- **Multi‑Judge:** mean or trimmed mean across judges; allow human spot‑check (5–10%) for calibration.
- **Significance:** 95% CI via task‑bootstrap or replicate SE. If Δ<1 and CIs overlap ⇒ statistical tie; break by lower cost or higher validator pass‑rate.

---

## 6) APIs & Interfaces
- **CLI**: `bench run --manifest path`, `bench report --run RUN_ID`, `bench export --format parquet`.
- **Python SDK**: `run = bench.load_manifest(...); bench.execute(run)`.
- **REST (optional)**:
  - `POST /runs` (manifest) → `run_id`
  - `GET /runs/{run_id}/status`
  - `POST /runs/{run_id}/resume`
  - `GET /runs/{run_id}/report` (CSV/MD/JSON)

---

## 7) Storage & Indexing (Relational)
- **Tables**: runs, tasks, inputs, prompts, models, generations (idx by run_id, task_id, prompt_id, model_id), validator_results (idx gen_id), judge_packets, judge_scores (idx packet_id), aggregates.
- **Artifacts**: `/runs/{run_id}/gen/{gen_id}.json`, `/runs/{run_id}/reports/*.md`.

---

## 8) Reproducibility & Compliance
- Content‑hash all prompts, judge prompts, validators, and manifests.
- Persist model names/versions, decoding params, seeds, and judge model versions.
- No chain‑of‑thought capture or leakage (final answers only).
- Full audit log: who/when/what for retries and overrides.

---

## 9) Non‑Functional Requirements
- **Determinism:** Seeds respected where providers allow.
- **Scalability:** 10k–100k generations per run; horizontal worker pool.
- **Resilience:** Idempotent job IDs; exactly‑once persistence; retry with jitter.
- **Latency/Cost Tracking:** Per‑gen and per‑run aggregation; exportable.
- **Security:** KMS‑managed secrets; PII‑free by design; role‑based access for REST/CLI.

---

## 10) Definition of Done (System)
- Executes a full matrix (≥3 prompts × ≥4 LLMs × ≥3 replicates × ≥10 tasks).
- Produces blinded JudgePackets and multi‑judge scores with rationales.
- Emits Markdown tables + CSV/Parquet with per‑LLM rankings and 95% CIs.
- All configs pinned; artifacts and summaries reproducible from RunManifest.
- Automated resubmission for transient failures; N/A rows reasoned and logged.

---

## 11) Extensibility Hooks
- **Validators:** simple interface `run(gen) -> passed, details`.
- **Judges:** pluggable judge providers; can add human queue.
- **Weights Profiles:** e.g., `default-v1`, `creative-v1`.
- **Tie Policy:** configurable (cost vs. validator pass‑rate priority).

---

## 12) Open Decisions for Architect
- Orchestration choice (e.g., LangGraph flow vs. job queue executor).
- Storage engine (Postgres + S3‑compatible) and schema evolution strategy.
- CI method (bootstrap vs. replicate SE) and confidence level defaults.
- REST exposure and auth model.
