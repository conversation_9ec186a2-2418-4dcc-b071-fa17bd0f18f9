## How I’ll run this like a real benchmark (not a vibes check)

- **Define tasks and acceptance criteria**, then collect prompt variants.
- **Blind, replicate, and randomize** all prompt × LLM runs.
- **Auto‑validate outputs**, then score with a calibrated LLM‑as‑Judge using explicit rubrics.
- **Normalize to 0–100**, aggregate with weights, and run basic significance checks.
- **Produce side‑by‑side tables**, rank best prompts per LLM, and log everything for reproducibility.

### Overview of Strategy

We evaluate multiple prompt‑writing strategies (your “prompt variants,” each from a different LLM or author) across several target LLMs. For each prompt × LLM pair, we run replicated generations, validate outputs against task‑specific checks, and score the generations with a double‑blind LLM‑as‑Judge guided by a stringent rubric. Scores are normalized to 0–100 and aggregated (weighted mean) to enable direct comparisons and per‑LLM recommendations. The whole process is fully reproducible via a run manifest (models, versions, temperatures, seeds, prompts, and judge prompts all pinned).

### Step-by-Step Process

### 1) Experiment setup (prompts, tasks, models)

1. **Task suite & acceptance criteria.** Curate a balanced set (e.g., instruction‑following, extraction, summarization, reasoning/math, coding, creative). For each task, define explicit acceptance criteria and any ground truth or validators (schemas, unit tests, regex checks, numerical answers).
2. **Prompt variants.** Gather N ≥ 3 distinct strategies (e.g., Structured Checklist, Few‑shot Focused, Self‑Ask/Decompose, Minimalist Direct). Standardize placeholders (e.g., `{INPUT}`, `{STYLE}`, `{CONSTRAINTS}`).
3. **Targets.** Pin model families and versions (e.g., GPT‑5, Claude, Mistral, Llama). Fix decoding params (e.g., temperature, top_p) and replicates (k ≥ 3).
4. **Run manifest.** Create a YAML/JSON manifest logging: prompt text, anonymized `Prompt_ID`, task instance ID, model/version, parameters, seeds, and evaluation plan.

### 2) Bias controls (double‑blind & randomization)

5. **Double‑blind.** Judges see the base task spec + acceptance criteria + the evaluated prompt text (anonymized) + the model’s output. They do not see which LLM wrote the prompt nor which LLM produced the output.
6. **Order randomization.** Randomize prompt order, candidate order (for A/B/C), and replicate ordering. Hash `Prompt_ID`s to conceal authorship.
7. **Multi‑judge.** Use ≥ 2 judges from different families (e.g., GPT + Claude) and aggregate (mean or trimmed mean). Add a small human spot‑check (e.g., 5–10%) to calibrate.

### 3) Evaluation framework (rubrics, weights, validators)

8. **Hybrid scoring.** Combine rule‑based checks (schemas, unit tests, exact‑match/F1, numeric tolerances) with LLM‑as‑Judge rubric ratings.
9. **Rubrics (0–100 anchors).** Judges must produce integers in [0,100] per metric using the anchors below (explicit rubrics appear after this section). They must cite which acceptance criteria passed or failed in a short note.
10. **Metric weights (default).**

   - Accuracy 25% · Adherence 20% · Completeness 20% · Clarity/Coherence 15% · Conciseness 10% · Creativity 10% (only if relevant; if not, mark N/A and renormalize remaining weights).
   - `Mean_Score` = weighted mean of available metrics.

11. **Replicate aggregation.** For each prompt × LLM × task: average metric scores across replicates; report the per‑pair `Mean_Score` and also store std/SE for stats.

### 4) Automation workflow (systematize & validate)

12. **Orchestrate.** The controller reads the run manifest, dispatches generations, and writes artifacts: raw outputs, validator results, judge packets, judge scores, and summaries.
13. **Auto‑validators.** Before judging, run: schema/JSON validation, key‑phrase checks, count checks, code/unit tests, numeric tolerance, and length limits. Store pass/fail flags.
14. **Judge packets.** Provide: task spec, acceptance criteria, (anonymized) prompt text used, model output, and validator results. Instruct the judge to ground decisions in criteria only.
15. **Completeness checks.** After each batch/table: assert no missing metrics for scored rows; if missing, mark N/A with a short cause and next step (e.g., “API timeout → resubmit with backoff”).

### 5) Comparative analysis (ranking & stats)

16. **Per‑LLM ranking.** For each target LLM, compute the overall `Mean_Score` across all tasks for each `Prompt_ID`.
17. **Significance & ties.** Compute 95% CIs by task‑level bootstrapping or across replicates. If top prompts differ by < 1 point and CIs overlap, mark a statistical tie and prefer the one with (a) lower token cost or (b) higher pass rate on hard acceptance checks.
18. **Error analysis.** Tag common failures (e.g., format drift, hallucination, over‑length). Use tags to refine prompts in the next iteration.

### 6) LLM-as-Judge rubric (explicit, task-agnostic)

Use these anchors for every metric; judges must align ratings to the closest anchor and can interpolate:

- **Accuracy**: 0–19 (mostly wrong); 20–39 (frequent errors); 40–59 (mixed correctness); 60–79 (mostly correct, minor issues); 80–89 (very accurate, tiny issues); 90–100 (fully correct or within tolerance).
- **Adherence**: 0–19 (ignores instructions); … ; 80–89 (minor deviations); 90–100 (follows all instructions precisely, formatting exact).
- **Completeness**: 0–19 (major gaps); … ; 80–89 (covers almost everything); 90–100 (all required points handled).
- **Clarity/Coherence**: 0–19 (ill‑formed); … ; 80–89 (clear, well‑organized); 90–100 (crisp, logically flowing; easy to follow).
- **Creativity/Originality (when applicable)**: 0–19 (generic/template); … ; 80–89 (fresh but appropriate); 90–100 (distinctive yet on‑brief).
- **Conciseness**: 0–19 (rambling/excessive); … ; 80–89 (tight with minor fluff); 90–100 (minimal, no redundancy).

Judge instruction highlights: never infer hidden requirements; evaluate only against the supplied acceptance criteria and the visible output; do not speculate about the producing model; provide a 1–2 sentence rationale per metric.

---

### Scoring Rubric Table (Markdown)

Example with hypothetical results (weights applied as defined). `Mean_Score` is the weighted mean. Creativity is applicable for these tasks; for any N/A, renormalize weights and mark clearly.

| Prompt_ID                     | Target_LLM      | Accuracy (0–100) | Adherence (0–100) | Completeness (0–100) | Clarity/Coherence (0–100) | Creativity/Originality (0–100 or N/A) | Conciseness (0–100) | Mean_Score (0–100) | Notes                                              |
|------------------------------|-----------------|------------------|-------------------|----------------------|----------------------------|----------------------------------------|---------------------|--------------------|---------------------------------------------------|
| P-001 (Structured Checklist) | GPT-5           | 92               | 96                | 94                   | 90                         | 70                                     | 85                  | 90.0               | Very strong compliance; slightly verbose.         |
| P-002 (Few-shot Focused)     | GPT-5           | 88               | 90                | 89                   | 92                         | 78                                     | 82                  | 87.6               | Great clarity from exemplars.                      |
| P-003 (Self-Ask Decompose)   | GPT-5           | 86               | 88                | 90                   | 88                         | 82                                     | 80                  | 86.5               | Good reasoning, a bit long.                        |
| P-001 (Structured Checklist) | Claude 3.7      | 85               | 92                | 90                   | 88                         | 68                                     | 87                  | 86.4               | High adherence; mild rigidity.                     |
| P-002 (Few-shot Focused)     | Claude 3.7      | 90               | 94                | 92                   | 93                         | 80                                     | 88                  | 90.5               | Exemplars match Claude’s style.                    |
| P-003 (Self-Ask Decompose)   | Claude 3.7      | 87               | 90                | 90                   | 91                         | 76                                     | 83                  | 87.3               | Occasional overthinking.                           |
| P-001 (Structured Checklist) | Mistral Large   | 84               | 90                | 88                   | 86                         | 60                                     | 88                  | 84.3               | Solid but conservative.                            |
| P-002 (Few-shot Focused)     | Mistral Large   | 82               | 86                | 85                   | 87                         | 72                                     | 84                  | 83.4               | Better with compact examples.                      |
| P-003 (Self-Ask Decompose)   | Mistral Large   | 88               | 90                | 89                   | 86                         | 78                                     | 81                  | 86.6               | Decomposition helps accuracy.                      |
| P-001 (Structured Checklist) | Llama 4 405B    | 90               | 92                | 92                   | 89                         | 65                                     | 86                  | 87.8               | Strong formatting control.                         |
| P-002 (Few-shot Focused)     | Llama 4 405B    | 86               | 90                | 88                   | 90                         | 77                                     | 84                  | 86.7               | Good style mimicry.                                |
| P-003 (Self-Ask Decompose)   | Llama 4 405B    | 89               | 91                | 90                   | 87                         | 79                                     | 83                  | 87.7               | Reasoning aids coverage.                           |
| P-004 (Minimalist Direct)    | Mistral Large   | N/A              | N/A               | N/A                  | N/A                        | N/A                                    | N/A                 | N/A                | Not Attempted (API timeout). Next step: resubmit job with exponential backoff and capture 3 replicates. |

Validation after table generation: 12/13 rows scored successfully; 1 row marked Not Attempted with a corrective next step documented above.

---

### Final Comparative Ranking Table (Markdown)

Per‑model recommendation using the highest `Mean_Score`; ties within ±1.0 point are flagged as statistical ties.

| Target_LLM     | Best_Prompt_ID                  | Best_Mean_Score | Most_Effective_Strategy |
|----------------|----------------------------------|-----------------|-------------------------|
| GPT-5          | P-001 (Structured Checklist)     | 90.0            | Highly structured checklist with explicit acceptance tests and fixed output schema; keep temperature low and set a length cap. |
| Claude 3.7     | P-002 (Few-shot Focused)         | 90.5            | 2–3 concise, high‑quality exemplars + short rule block; emphasize tone/style constraints; avoid overly rigid templates. |
| Mistral Large  | P-003 (Self-Ask Decompose)       | 86.6            | Brief step‑wise decomposition + small exemplar; cap reasoning verbosity; include format guardrails. |
| Llama 4 405B   | P-001 (Structured Checklist)*    | 87.8            | *Statistical tie (Δ = 0.1) with P‑003. Prefer P‑001 for strict formatting; pick P‑003 for harder reasoning tasks.* |

---

### Notes on Reproducibility & Quality

- **Pin everything.** Log model names, versions, decoding params, seeds, prompts, judge prompts, and validators in the run manifest.
- **No chain‑of‑thought leakage.** Prompts request final answers only; judges evaluate outcomes, not hidden reasoning.
- **Stats sanity.** Report replicate count, mean, and optionally SE/95% CI per prompt × LLM; treat < 1‑point differences as ties unless CIs separate clearly.
- **Cost & latency tracking.** Record tokens and latency; use as secondary tie‑breakers.

If you like, I can adapt this template to your PromptReflect orchestration (manifests, judge packets, and validation hooks), or generate ready‑to‑run judge prompts and acceptance‑criteria templates for your specific task suite.

Here’s how I’ll run this like a real benchmark, not a vibes check.
	•	Define tasks and acceptance criteria, then collect prompt variants.
	•	Blind, replicate, and randomize all prompt × LLM runs.
	•	Auto‑validate outputs, then score with a calibrated LLM‑as‑Judge using explicit rubrics.
	•	Normalize to 0–100, aggregate with weights, and run basic significance checks.
	•	Produce side‑by‑side tables, rank best prompts per LLM, and log everything for reproducibility.

Overview of Strategy

We evaluate multiple prompt‑writing strategies (your “prompt variants,” each from a different LLM or author) across several target LLMs. For each prompt × LLM pair, we run replicated generations, validate outputs against task‑specific checks, and score the generations with a double‑blind LLM‑as‑Judge guided by a stringent rubric. Scores are normalized to 0–100 and aggregated (weighted mean) to enable direct comparisons and per‑LLM recommendations. The whole process is fully reproducible via a run manifest (models, versions, temperatures, seeds, prompts, and judge prompts all pinned).

Step-by-Step Process

1) Experiment setup (prompts, tasks, models)
	1.	Task suite & acceptance criteria. Curate a balanced set (e.g., instruction‑following, extraction, summarization, reasoning/math, coding, creative). For each task, define explicit acceptance criteria and any ground truth or validators (schemas, unit tests, regex checks, numerical answers).
	2.	Prompt variants. Gather N≥3 distinct strategies (e.g., Structured Checklist, Few‑shot Focused, Self‑Ask/Decompose, Minimalist Direct). Standardize placeholders (e.g., {INPUT}, {STYLE}, {CONSTRAINTS}).
	3.	Targets. Pin model families and versions (e.g., GPT‑5, Claude, Mistral, Llama). Fix decoding params (e.g., temperature, top_p) and replicates (k≥3).
	4.	Run manifest. Create a YAML/JSON manifest logging: prompt text, anonymized Prompt_ID, task instance ID, model/version, parameters, seeds, and evaluation plan.

2) Bias controls (double‑blind & randomization)
	5.	Double‑blind. Judges see the base task spec + acceptance criteria + the evaluated prompt text (anonymized) + the model’s output. They do not see which LLM wrote the prompt nor which LLM produced the output.
	6.	Order randomization. Randomize prompt order, candidate order (for A/B/C), and replicate ordering. Hash Prompt_IDs to conceal authorship.
	7.	Multi‑judge. Use ≥2 judges from different families (e.g., GPT + Claude) and aggregate (mean or trimmed mean). Add a small human spot‑check (e.g., 5–10%) to calibrate.

3) Evaluation framework (rubrics, weights, validators)
	8.	Hybrid scoring. Combine rule‑based checks (schemas, unit tests, exact‑match/F1, numeric tolerances) with LLM‑as‑Judge rubric ratings.
	9.	Rubrics (0–100 anchors). Judges must produce integers in [0,100] per metric using the anchors below (explicit rubrics appear after this section). They must cite which acceptance criteria passed or failed in a short note.
	10.	Metric weights (default).

	•	Accuracy 25% · Adherence 20% · Completeness 20% · Clarity/Coherence 15% · Conciseness 10% · Creativity 10% (only if relevant; if not, mark N/A and renormalize remaining weights).
	•	Mean_Score = weighted mean of available metrics.

	11.	Replicate aggregation. For each prompt × LLM × task: average metric scores across replicates; report the per‑pair Mean_Score and also store std/SE for stats.

4) Automation workflow (systematize & validate)
	12.	Orchestrate. The controller reads the run manifest, dispatches generations, and writes artifacts: raw outputs, validator results, judge packets, judge scores, and summaries.
	13.	Auto‑validators. Before judging, run: schema/JSON validation, key‑phrase checks, count checks, code/unit tests, numeric tolerance, and length limits. Store pass/fail flags.
	14.	Judge packets. Provide: task spec, acceptance criteria, (anonymized) prompt text used, model output, and validator results. Instruct the judge to ground decisions in criteria only.
	15.	Completeness checks. After each batch/table: assert no missing metrics for scored rows; if missing, mark N/A with a short cause and next step (e.g., “API timeout → resubmit with backoff”).

5) Comparative analysis (ranking & stats)
	16.	Per‑LLM ranking. For each target LLM, compute the overall Mean_Score across all tasks for each Prompt_ID.
	17.	Significance & ties. Compute 95% CIs by task‑level bootstrapping or across replicates. If top prompts differ by <1 point and CIs overlap, mark a statistical tie and prefer the one with (a) lower token cost or (b) higher pass rate on hard acceptance checks.
	18.	Error analysis. Tag common failures (e.g., format drift, hallucination, over‑length). Use tags to refine prompts in the next iteration.

6) LLM-as-Judge rubric (explicit, task-agnostic)

Use these anchors for every metric; judges must align ratings to the closest anchor and can interpolate:
	•	Accuracy: 0–19 (mostly wrong); 20–39 (frequent errors); 40–59 (mixed correctness); 60–79 (mostly correct, minor issues); 80–89 (very accurate, tiny issues); 90–100 (fully correct or within tolerance).
	•	Adherence: 0–19 (ignores instructions); … ; 80–89 (minor deviations); 90–100 (follows all instructions precisely, formatting exact).
	•	Completeness: 0–19 (major gaps); … ; 80–89 (covers almost everything); 90–100 (all required points handled).
	•	Clarity/Coherence: 0–19 (ill‑formed); … ; 80–89 (clear, well‑organized); 90–100 (crisp, logically flowing; easy to follow).
	•	Creativity/Originality (when applicable): 0–19 (generic/template); … ; 80–89 (fresh but appropriate); 90–100 (distinctive yet on‑brief).
	•	Conciseness: 0–19 (rambling/excessive); … ; 80–89 (tight with minor fluff); 90–100 (minimal, no redundancy).
Judge instruction highlights: never infer hidden requirements; evaluate only against the supplied acceptance criteria and the visible output; do not speculate about the producing model; provide a 1–2 sentence rationale per metric.

⸻

Scoring Rubric Table (Markdown)

Example with hypothetical results (weights applied as defined). “Mean_Score” is the weighted mean. Creativity is applicable for these tasks; for any N/A, renormalize weights and mark clearly.

Prompt_ID	Target_LLM	Accuracy (0–100)	Adherence (0–100)	Completeness (0–100)	Clarity/Coherence (0–100)	Creativity/Originality (0–100 or N/A)	Conciseness (0–100)	Mean_Score (0–100)	Notes
P-001 (Structured Checklist)	GPT-5	92	96	94	90	70	85	90.0	Very strong compliance; slightly verbose.
P-002 (Few-shot Focused)	GPT-5	88	90	89	92	78	82	87.6	Great clarity from exemplars.
P-003 (Self-Ask Decompose)	GPT-5	86	88	90	88	82	80	86.5	Good reasoning, a bit long.
P-001 (Structured Checklist)	Claude 3.7	85	92	90	88	68	87	86.4	High adherence; mild rigidity.
P-002 (Few-shot Focused)	Claude 3.7	90	94	92	93	80	88	90.5	Exemplars match Claude’s style.
P-003 (Self-Ask Decompose)	Claude 3.7	87	90	90	91	76	83	87.3	Occasional overthinking.
P-001 (Structured Checklist)	Mistral Large	84	90	88	86	60	88	84.3	Solid but conservative.
P-002 (Few-shot Focused)	Mistral Large	82	86	85	87	72	84	83.4	Better with compact examples.
P-003 (Self-Ask Decompose)	Mistral Large	88	90	89	86	78	81	86.6	Decomposition helps accuracy.
P-001 (Structured Checklist)	Llama 4 405B	90	92	92	89	65	86	87.8	Strong formatting control.
P-002 (Few-shot Focused)	Llama 4 405B	86	90	88	90	77	84	86.7	Good style mimicry.
P-003 (Self-Ask Decompose)	Llama 4 405B	89	91	90	87	79	83	87.7	Reasoning aids coverage.
P-004 (Minimalist Direct)	Mistral Large	N/A	N/A	N/A	N/A	N/A	N/A	N/A	Not Attempted (API timeout). Next step: resubmit job with exponential backoff and capture 3 replicates.

Validation after table generation: 12/13 rows scored successfully; 1 row marked Not Attempted with a corrective next step documented above.

⸻

Final Comparative Ranking Table (Markdown)

Per‑model recommendation using the highest Mean_Score; ties within ±1.0 point are flagged as statistical ties.

Target_LLM	Best_Prompt_ID	Best_Mean_Score	Most_Effective_Strategy
GPT-5	P-001 (Structured Checklist)	90.0	Highly structured checklist with explicit acceptance tests and fixed output schema; keep temperature low and set a length cap.
Claude 3.7	P-002 (Few-shot Focused)	90.5	2–3 concise, high‑quality exemplars + short rule block; emphasize tone/style constraints; avoid overly rigid templates.
Mistral Large	P-003 (Self-Ask Decompose)	86.6	Brief step‑wise decomposition + small exemplar; cap reasoning verbosity; include format guardrails.
Llama 4 405B	P-001 (Structured Checklist)*	87.8	*Statistical tie (Δ=0.1) with P‑003. Prefer P‑001 for strict formatting; pick P‑003 for harder reasoning tasks.


⸻

Notes on Reproducibility & Quality
	•	Pin everything. Log model names, versions, decoding params, seeds, prompts, judge prompts, and validators in the run manifest.
	•	No chain‑of‑thought leakage. Prompts request final answers only; judges evaluate outcomes, not hidden reasoning.
	•	Stats sanity. Report replicate count, mean, and optionally SE/95% CI per prompt × LLM; treat <1‑point differences as ties unless CIs separate clearly.
	•	Cost & latency tracking. Record tokens and latency; use as secondary tie‑breakers.


