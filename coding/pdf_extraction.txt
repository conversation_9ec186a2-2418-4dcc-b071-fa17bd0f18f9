# Table extraction from pdf
// please write a python script to extract tables from a pdf file using tabula.
// table extraction should take following parameters:
// - pdf file path
// - output file path
// - page number (optional)
// - regex to mark start of table (optional)
// - regex to mark end of table (optional)
// - table page (optional)
// - table columns (optional)

// please use tabula-py library to extract tables from pdf.
// please use loguru to log the errors, warnings, info, debug messages etc.
// please use pdfminer to extract the table area coordinates from pdf using the page number and regex to mark start and end of table.


# PDF table extraction application
You are an AI software architect working on pdf table extraction application development project.
Your role is to help the engineer research various pdf table extraction libraries and tools. Identify the most suitable library to use for the project.
Also, identify the most suitable UI library to use for the project.
Tables to be extracted are visually clear but have no grids or lines to separate columns.
Tables have clear column headers which are aligned to the left. Column boundaries can be calculated by the difference in the x-coordinates of the column headers.
x-coordinates are the coordinates of the leftmost point of the column header and the rightmost point of the column can be calculated from the x-coordinates of the next column header.
1. Parser - to extract tables from pdf and save them as pandas dataframe.
2. UI - to display the tables in a table format. UI should be interactive and user friendly to analyze the parser output.

# PDF table extraction application (architect)
You are an AI software architect working on pdf table extraction application development project.
Your role is to help the engineer research various pdf table extraction libraries and tools. Identify the most suitable library to use for the project.
Also, identify the most suitable UI library to use for the project.
Tables to be extracted are visually clear but have no grids or lines to separate columns.
Tables have clear column headers which are aligned to the left.
App should have following two components:
1. Parser - to extract tables from pdf and save them as pandas dataframe.
2. UI - to display the tables in a table format. UI should be interactive and user friendly to analyze the parser output.


# PDF table extraction application (engineer)
You are an AI engineer working on pdf table extraction application development project.
Your role is to implement the parser and UI components of the application.
Please use pdfplumber and pymupdf libraries to extract the tables from pdf.
Please use pandas to save the tables as dataframe.
Please use streamlit library to implement the UI.
Tables to be extracted are visually clear but have no grids or lines to separate columns.
Tables have clear column headers which are aligned to the left. Column boundaries can be calculated by the difference in the x-coordinates of the column headers.
x-coordinates are the coordinates of the leftmost point of the column header and the rightmost point of the column can be calculated from the x-coordinates of the next column header.
1. Parser - to extract tables from pdf and save them as pandas dataframe.
2. UI - to display the tables in a table format. UI should be interactive and user friendly to analyze the parser output.
Write the skeleton code for the parser to extract the tables from pdf and save them as dataframe.
Classes and methods should cover combination of different strategies to extract the tables from pdf.
Please only write the skeleton code for the parser.



# Please write python function find_column_boundaries_using_char(grouped_words_df, header_config)

# PDF Cropping using pdfplumber
// please write a python function called crop_pdf_using_coordinates(pdf_path, coordinates, output_path, page_number)
// this function should take the pdf path, coordinates and output path as input and crop the pdf using the coordinates and save the cropped pdf to the output path.
// coordinates is a list of tuples, where each tuple contains the x1, y1, x2, y2 coordinates of the pdf.
// x1, y1 is the top left corner of the pdf and x2, y2 is the bottom right corner of the pdf.
// please use pdfplumber library to crop the pdf.
// page_number is the page number of the pdf to be cropped.

// please also write a python function called derive_cordinates_from_grouped_words_df(grouped_words_df)
// this function should take the grouped_words_df as input and return the coordinates of the pdf.
// grouped_words_df is a pandas dataframe of pdfplumber extract_words() output grouped using top with the following sample data:
top,x0,x1,text_list
51.**************,"[235.*************, 270.*************, 309.*************, 317.*************]","[265.************, 296.**************, 313.*************, 361.**************]","['Account', 'Number', ':', '**********']"
// please extract the x0, x1, y0, y1 coordinates from dataframe and return them as a list of tuples.

// please also add method crop_pdf_using_grouped_words_df(pdf_file_path, grouped_words_df, output_file_path) to the pdf_cropping class.
// please call derive_cordinates_from_grouped_words_df to get the coordinates from the grouped_words_df and pass them to crop_pdf_using_coordinates to crop the pdf.




import pdfplumber

from pdf_tables_app.logger_config import logger


class PdfCropping:
    def __init__(self):
        self.logger = logger

    def derive_coordinates_from_grouped_words_df(self, grouped_words_df):
        """
        Derive coordinates from grouped words dataframe
        Args:
            grouped_words_df: Pandas dataframe containing grouped words from PDF
        Returns:
            List of tuples containing coordinates (x1,y1,x2,y2)
        """
        self.logger.info("Starting coordinate derivation from grouped words dataframe")
        self.logger.debug(f"Input dataframe shape: {grouped_words_df.shape}")

        coordinates = []
        for idx, row in grouped_words_df.iterrows():
            try:
                # Get min x0 and max x1 from the lists
                x0_list = eval(row["x0"]) if isinstance(row["x0"], str) else row["x0"]
                x1_list = eval(row["x1"]) if isinstance(row["x1"], str) else row["x1"]

                x1 = min(x0_list)  # Left-most x coordinate
                x2 = max(x1_list)  # Right-most x coordinate

                # Use top value for y coordinates with some padding
                y1 = float(row["top"]) - 5  # Add padding above
                y2 = float(row["top"]) + 15  # Add padding below

                coordinates.append((x1, y1, x2, y2))
                self.logger.debug(
                    f"Processed row {idx}: Coordinates = ({x1}, {y1}, {x2}, {y2})"
                )

            except Exception as e:
                self.logger.error(f"Error processing row {idx}: {str(e)}")
                raise

        self.logger.info(f"Successfully derived {len(coordinates)} coordinate sets")
        return coordinates

    def crop_pdf_using_coordinates(
        self, pdf_path, coordinates, output_path, page_number=0
    ):
        """
        Crop PDF using provided coordinates
        Args:
            pdf_path: Path to input PDF file
            coordinates: List of tuples containing coordinates (x1,y1,x2,y2)
            output_path: Path to save cropped PDF
            page_number: Page number to crop (default 0)
        """
        self.logger.info(f"Starting PDF cropping for file: {pdf_path}")
        self.logger.debug(f"Using coordinates: {coordinates}")

        try:
            with pdfplumber.open(pdf_path) as pdf:
                self.logger.debug(
                    f"Successfully opened PDF with {len(pdf.pages)} pages"
                )

                if page_number >= len(pdf.pages):
                    error_msg = f"Page number {page_number} exceeds PDF length"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                page = pdf.pages[page_number]
                self.logger.info(f"Processing page {page_number}")

                # Create list to store cropped images
                cropped_images = []

                for i, coord in enumerate(coordinates):
                    x1, y1, x2, y2 = coord
                    self.logger.debug(
                        f"Cropping region {i + 1}: ({x1}, {y1}, {x2}, {y2})"
                    )

                    # Crop the page using coordinates
                    cropped = page.crop(bbox=(x1, y1, x2, y2))

                    # Convert cropped area to image
                    img = cropped.to_image()
                    cropped_images.append(img)
                    self.logger.debug(f"Successfully cropped region {i + 1}")

                # Save the first cropped image
                if cropped_images:
                    cropped_images[0].save(output_path)
                    self.logger.info(
                        f"Successfully saved cropped PDF to: {output_path}"
                    )
                else:
                    self.logger.warning("No regions were cropped from the PDF")

        except Exception as e:
            self.logger.error(f"Error during PDF cropping: {str(e)}")
            raise

    def crop_pdf_using_grouped_words_df(
        self, pdf_file_path, grouped_words_df, output_file_path=None, page_number=0
    ):
        """
        Crop PDF using grouped words dataframe
        Args:
            pdf_file_path: Path to input PDF file
            grouped_words_df: Pandas dataframe containing grouped words
            output_file_path: Path to save cropped PDF
            page_number: Page number to crop (default 0)
        """
        self.logger.info("Starting PDF cropping using grouped words dataframe")

        try:
            if output_file_path is None:
                output_file_path = pdf_file_path.replace(".pdf", "_cropped.pdf")

            # Get coordinates from dataframe
            self.logger.info("Deriving coordinates from dataframe")
            coordinates = self.derive_coordinates_from_grouped_words_df(
                grouped_words_df
            )

            # Crop PDF using coordinates
            self.logger.info("Initiating PDF cropping with derived coordinates")
            self.crop_pdf_using_coordinates(
                pdf_file_path, coordinates, output_file_path, page_number
            )

            self.logger.info("PDF cropping process completed successfully")

        except Exception as e:
            self.logger.error(f"Error in crop_pdf_using_grouped_words_df: {str(e)}")
            raise
