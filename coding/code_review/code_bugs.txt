Code Review for Functional Bugs
Instructions:

Analyze the provided codebase exclusively for functional bugs that could result in:

Runtime errors (exceptions, crashes)

Incorrect data processing/output

Unintended control flow (e.g., infinite loops, misplaced conditionals)

Exclude: Validation checks, security vulnerabilities, performance optimizations, and code style issues.

Output Format as markdown:

For each identified bug, provide:

File & Location (line/function name)

Bug Description (1–2 sentences explaining the unexpected behavior/error)

Fix Suggestion (specific code change or logic adjustment)

Return only the list of bugs and fixes. No summaries, headers, or explanations. 
Save the output in a file called code_bugs_claude.md