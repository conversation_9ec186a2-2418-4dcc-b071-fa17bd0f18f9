# Code analysis using graphs and diagrams
// You are an AI software architect. Your role is to architect a code analysis tool or system that can analyse the code and generate graphs and diagrams.
// You can use any python library to generate the graphs and diagrams. 
// You can also use multiple tools and utilities for the task.
// You can consider storing the graph data in database like Neo4j, if it may help in analysis.
Purpose of the tool:
1. To analyse the code and understand the code structure and complexity.
2. To understand the dependencies between the modules.
3. To understand the flow of the code.
4. To understand the code quality and identify the potential issues.
5. To view the code structure and dependencies in a visual way.
6. To help identify the unused code.
7. To help identify the potential refactoring opportunities.


Please only focus on the code analysis using graphs and diagrams. 


# Refined Prompt

Design a Python-based code analysis tool or system that generates interactive graphs and diagrams to visualize:

Code Structure: Class/method hierarchies (using UML diagrams via pyreverse or graphviz).

Dependencies: Module/function-level relationships (via directed graphs with networkx/pyvis).

Code Flow: Call graphs and control flow (using pyan or custom AST traversal).

Quality Metrics: Cyclomatic complexity, code duplication, and linting issues (integrate radon, pylint, or flake8), highlighted in heatmap-style diagrams.

Unused Code: Identify dead functions/variables via static analysis (e.g., vulture).

Refactoring Opportunities: Flag tightly coupled modules, long methods, or circular dependencies with annotated visual markers.

Requirements:

Parse codebases into ASTs (using libcst or ast module).

Output interactive diagrams (HTML/JS via plotly/bokeh) and static reports (PDF/SVG).

Prioritize modularity to support extensibility (e.g., plug-in architecture for new analyzers).

Exclude non-graph-based analysis (e.g., raw text reports).

UI to display the graphs and diagrams which should be interactive and user friendly.

Please return the suggested architecture as markdown file.