
{# 
# Comparative Research Prompt Template (Optimized)
**Expected Inputs:**
- `frameworks`: An ordered list of framework names as strings (e.g., `["LangGraph", "Autogen Agentic Framework"]`).
- `time_window`: A string specifying the relevant data range (default: `3 months`).
- `factor_weights`: **(optional)** Object mapping focus factors to numeric weights (e.g., `{"Developer UX": 0.4, "Maturity & Activity": 0.35, "Use-Case Fit": 0.25}`).  
  - If omitted, default factors are `["User Experience", "GitHub Activity", "Intended Use Cases"]` with equal weights.
#}

## 🔍 Research Prompt: {{ frameworks | join(' vs ') }}

**🎯 Focus Factors (ordered):**  
{% if factor_weights is defined and factor_weights is mapping %}
{{ factor_weights.keys() | list | join(', ') }}  
**Weights:** {{ factor_weights }}
{% else %}
{{ ['User Experience','GitHub Activity','Intended Use Cases'] | join(', ') }}
{% endif %}

Begin with a concise checklist (3–7 bullets) of the key high-level areas you will cover in the analysis; keep items conceptual, not implementation-level. Align this checklist with the **Focus Factors** above.

Perform a comparative analysis of the following frameworks:
{% for fw in frameworks -%}
**{{ fw }}**{% if not loop.last %} and {% endif %}
{%- endfor %},
**focusing on the Focus Factors** listed above, using information available as of {{ time_window | default('3 months') }}.

For each focus factor, briefly define what “good” looks like, what evidence you will use (e.g., user reports, docs, benchmarks, release cadence), and then assign a **1–5 score** per framework with 1–2 lines of evidence.

---

### 1. 🧑‍💻 User Feedback & Issues
- Search for and review recent user feedback from blogs, forums (e.g., Reddit, Stack Overflow, Discord), and social media for each framework during the specified time window.
- Summarize the most common issues, limitations, and pain points developers have reported for each framework.

---

### 2. 📊 GitHub Metrics & Project Activity
- Explore the GitHub repositories for each framework.
- Collect and compare:
  - ⭐ Star count
  - 📅 Last commit or release date
  - 📈 Commit frequency over the past 3–6 months
- Use this data to assess project vitality and community engagement.

---

### 3. 📚 Documentation & Use Cases
- Analyze the official documentation of each framework for:
  - Core features and major use cases
  - Documented limitations, assumptions, or incompatibilities
- Identify optimal scenarios based on the framework's intended design and documentation.

---

### 4. 🎯 Focus Factor Scoring (1–5) & Evidence
For each **focus factor** in **order**, provide per-framework scores and short evidence. Use equal weights unless `factor_weights` is provided.

- For each factor in {% if factor_weights is defined and factor_weights is mapping %}{{ factor_weights.keys() | list | join(', ') }}{% else %}{{ ['User Experience','GitHub Activity','Intended Use Cases'] | join(', ') }}{% endif %}:
  - Define measurement proxies (what you will look at).
  - Score each framework **1–5** (5 = excellent).
  - Cite 1–2 concrete pieces of evidence supporting the score (link, quote, or metric).

Provide a **Score Matrix** (frameworks in the original input order; factors as columns in the order provided) and compute **Weighted Totals** if `factor_weights` is supplied; otherwise, compute simple averages.

---

### 5. ⚖️ Framework Selection: Comparative Summary
- Based on user feedback, GitHub data, documentation, **and the Focus Factor scores**, describe clear scenarios or conditions where each framework is the best fit.
- Support recommendations with specific evidence (e.g., user sentiments, release frequency, architectural choices, or factor scores).

---

After compiling all findings, validate for each framework that **user feedback**, **GitHub activity metrics**, **documentation & use-case analysis**, and **focus factor scoring** are all covered. If any area lacks data, capture these gaps in the `Not Found` section. After compiling, provide a brief validation: note any missing elements, and if validation fails, either proceed noting gaps or attempt to correct them.

## Output Format
Present results in the following Markdown structure, keeping the original input order from the `frameworks` list for all comparisons.

- **Focus Factors**
  - **Factors (ordered)**: `{% if factor_weights is defined and factor_weights is mapping %}{{ factor_weights.keys() | list | join(', ') }}{% else %}{{ ['User Experience','GitHub Activity','Intended Use Cases'] | join(', ') }}{% endif %}`
  - **Weights**: `<JSON object or omit if equal weights>`

- **Comparison**
  - **Framework**: `<name>`
    - **User Feedback**:
      - **Summary**: `<brief summary>`
      - **Common Pain Points**: `[<issue1>, <issue2>, ...]` (empty array if none)
      - **Notable Quotes**: `[<quote1>, ...]` (omit field entirely if none found)
    - **GitHub Metrics**:
      - **Star Count**: `<number or null>`
      - **Last Commit Date**: `<YYYY-MM-DD or null>`
      - **Commit Frequency**: `<description or null>`
      - **Notes**: `<optional string>` (omit if not present)
    - **Documentation**:
      - **Core Capabilities**: `<summary>`
      - **Limitations**: `[<limitation1>, ...]` (empty array if none)
      - **Intended Use Cases**: `[<use case1>, ...]` (empty array if none)
    - **Focus Factor Scores**:
      {% if factor_weights is defined and factor_weights is mapping %}
      {% for factor in factor_weights.keys() %}
      - **{{ factor }}**:
        - **Score**: `<1–5 or null>`
        - **Evidence**: `<1–2 sentences or bullet points>`
      {% endfor %}
      {% else %}
      {% for factor in ['User Experience','GitHub Activity','Intended Use Cases'] %}
      - **{{ factor }}**:
        - **Score**: `<1–5 or null>`
        - **Evidence**: `<1–2 sentences or bullet points>`
      {% endfor %}
      {% endif %}

- **Focus Factor Score Matrix**
  - Provide a markdown table with frameworks as rows (input order) and factors as columns (focus factor order). Include a **Weighted Total** column if `factor_weights` is provided; otherwise include an **Average** column.

- **Comparative Summary**
  - For each framework:
    - **Framework**: `<name>`
    - **Recommended When**:
      - **Scenario**: `<when to choose>`
      - **Evidence**: `<supporting data>` (string or bullet list)
  - **Per-Factor Leaders**: `{ "<factor>": "<framework name>", ... }`
  - **Overall Ranking**: `["<framework1>", "<framework2>", ...]` with associated totals/averages

- **Not Found**
  - Provide a JSON object with keys mapping to arrays of framework names, indicating where data was missing:
    - `no_github`: frameworks lacking GitHub data
    - `no_user_feedback`: frameworks with no found user feedback
    - `no_docs`: frameworks missing documentation
    - `no_focus_factors`: frameworks missing focus factor scores

#### Example Not Found Format
```json
{
  "no_github": ["FrameworkX"],
  "no_user_feedback": ["FrameworkY"],
  "no_docs": [],
  "no_focus_factors": []
}
```

If an optional field (such as Notable Quotes, Notes, or Weights) is empty, omit it. Use arrays for lists, and `null` for expected but missing values in required fields, as outlined.
